/*
 * Function: ?Login@CBilling@@UEAAXPEAVCUserDB@@@Z
 * Address: 0x14028CAC0
 */

void __fastcall CBilling::Login(CBilling *this, CUserDB *pUserDB)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-68h]@1
  __int16 v6; // [sp+20h] [bp-48h]@4
  _SYSTEMTIME *v7; // [sp+28h] [bp-40h]@4
  int v8; // [sp+30h] [bp-38h]@4
  _SYSTEMTIME *v9; // [sp+40h] [bp-28h]@4
  char *v10; // [sp+48h] [bp-20h]@4
  CBillingVtbl *v11; // [sp+50h] [bp-18h]@4
  CBilling *v12; // [sp+70h] [bp+8h]@1
  CUserDB *v13; // [sp+78h] [bp+10h]@1

  v13 = pUserDB;
  v12 = this;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = &pUserDB->m_BillingInfo.stEndDate;
  v10 = pUserDB->m_BillingInfo.szCMS;
  v4 = inet_ntoa((struct in_addr)pUserDB->m_dwIP);
  v11 = v12->vfptr;
  v8 = v13->m_BillingInfo.lRemainTime;
  v7 = v9;
  v6 = v13->m_BillingInfo.iType;
  if ( (unsigned __int8)((int (__fastcall *)(CBilling *, signed __int64, char *, char *))v11->SendMsg_Login)(
                          v12,
                          (signed __int64)v13->m_szAccountID,
                          v4,
                          v10) )
    CUserDB::SetBillingNoLogout(v13, 0);
}
