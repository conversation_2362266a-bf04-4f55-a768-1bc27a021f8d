/*
 * Function: ?ValidateGroup@DL_GroupParameters_IntegerBased@CryptoPP@@UEBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x140630680
 */

char __fastcall CryptoPP::DL_GroupParameters_IntegerBased::ValidateGroup(CryptoPP::DL_GroupParameters_IntegerBased *this, struct CryptoPP::RandomNumberGenerator *a2, unsigned int a3)
{
  CryptoPP::Integer *v3; // rax@1
  CryptoPP::Integer *v4; // rax@1
  const struct CryptoPP::Integer *v5; // rax@1
  unsigned int v6; // er9@1
  const struct CryptoPP::Integer *v7; // rax@6
  const struct CryptoPP::Integer *v8; // rax@12
  CryptoPP::Integer *v9; // rax@12
  CryptoPP::Integer *v10; // rax@13
  CryptoPP::Integer *v11; // rax@13
  unsigned int v12; // er9@24
  CryptoPP::Integer *v14; // [sp+20h] [bp-118h]@1
  CryptoPP::Integer *b; // [sp+28h] [bp-110h]@1
  char v16; // [sp+30h] [bp-108h]@10
  CryptoPP::Integer v17; // [sp+38h] [bp-100h]@12
  CryptoPP::Integer v18; // [sp+60h] [bp-D8h]@13
  CryptoPP::Integer result; // [sp+88h] [bp-B0h]@13
  int v20; // [sp+B0h] [bp-88h]@1
  __int64 v21; // [sp+B8h] [bp-80h]@1
  __int64 v22; // [sp+C0h] [bp-78h]@1
  CryptoPP::ASN1ObjectVtbl *v23; // [sp+C8h] [bp-70h]@1
  int v24; // [sp+D0h] [bp-68h]@3
  int v25; // [sp+D4h] [bp-64h]@8
  const struct CryptoPP::Integer *v26; // [sp+D8h] [bp-60h]@12
  CryptoPP::ASN1ObjectVtbl *v27; // [sp+E0h] [bp-58h]@12
  CryptoPP::Integer *v28; // [sp+E8h] [bp-50h]@12
  CryptoPP::Integer *v29; // [sp+F0h] [bp-48h]@12
  CryptoPP::Integer *v30; // [sp+F8h] [bp-40h]@13
  CryptoPP::ASN1ObjectVtbl *v31; // [sp+100h] [bp-38h]@13
  CryptoPP::Integer *v32; // [sp+108h] [bp-30h]@13
  CryptoPP::Integer *a; // [sp+110h] [bp-28h]@13
  CryptoPP::Integer *v34; // [sp+118h] [bp-20h]@13
  CryptoPP::Integer *v35; // [sp+120h] [bp-18h]@13
  int v36; // [sp+128h] [bp-10h]@14
  int v37; // [sp+12Ch] [bp-Ch]@26
  CryptoPP::DL_GroupParameters_IntegerBased *v38; // [sp+140h] [bp+8h]@1
  CryptoPP *v39; // [sp+148h] [bp+10h]@1
  unsigned int v40; // [sp+150h] [bp+18h]@1

  v40 = a3;
  v39 = (CryptoPP *)a2;
  v38 = this;
  v21 = -2i64;
  v20 = 0;
  v22 = *(_QWORD *)&this[-1].gap48[8];
  LODWORD(v3) = (*(int (__fastcall **)(_BYTE *))(v22 + 32))(&this[-1].gap48[8]);
  v14 = v3;
  v23 = v38->vfptr;
  LODWORD(v4) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters_IntegerBased *))v23[2].__vecDelDtor)(v38);
  b = v4;
  LODWORD(v5) = CryptoPP::Integer::One();
  v24 = CryptoPP::operator>(v14, v5) && CryptoPP::Integer::IsOdd(v14);
  v25 = (_BYTE)v24
     && (LODWORD(v7) = CryptoPP::Integer::One(), CryptoPP::operator>(b, v7))
     && CryptoPP::Integer::IsOdd(b);
  v16 = v25;
  if ( v40 >= 1 )
  {
    v36 = (_BYTE)v25
       && (LODWORD(v8) = CryptoPP::Integer::One(),
           v26 = v8,
           v27 = v38->vfptr,
           LODWORD(v9) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters_IntegerBased *, CryptoPP::Integer *))v27[2].BEREncode)(
                           v38,
                           &v17),
           v28 = v9,
           v29 = v9,
           v20 |= 1u,
           CryptoPP::operator>(v9, v26))
       && (LODWORD(v10) = CryptoPP::Integer::Zero(),
           v30 = v10,
           v31 = v38->vfptr,
           LODWORD(v11) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters_IntegerBased *, CryptoPP::Integer *))v31[2].DEREncode)(
                            v38,
                            &v18),
           v32 = v11,
           a = v11,
           v20 |= 2u,
           v34 = CryptoPP::operator%(&result, v11, b),
           v35 = v34,
           v20 |= 4u,
           CryptoPP::operator==(v34, v30));
    v16 = v36;
    if ( v20 & 4 )
    {
      v20 &= 0xFFFFFFFB;
      CryptoPP::Integer::~Integer(&result);
    }
    if ( v20 & 2 )
    {
      v20 &= 0xFFFFFFFD;
      CryptoPP::Integer::~Integer(&v18);
    }
    if ( v20 & 1 )
    {
      v20 &= 0xFFFFFFFE;
      CryptoPP::Integer::~Integer(&v17);
    }
  }
  if ( v40 >= 2 )
  {
    v37 = v16
       && CryptoPP::VerifyPrime(
            v39,
            (struct CryptoPP::RandomNumberGenerator *)b,
            (const struct CryptoPP::Integer *)(v40 - 2),
            v6)
       && CryptoPP::VerifyPrime(
            v39,
            (struct CryptoPP::RandomNumberGenerator *)v14,
            (const struct CryptoPP::Integer *)(v40 - 2),
            v12);
    v16 = v37;
  }
  return v16;
}
