@echo off
REM Build test script for RF Online Server
REM This script attempts to build the project using MSBuild

echo ========================================
echo RF Online Server - Build Test
echo ========================================
echo.

REM Check if Visual Studio 2022 is installed
set "VS2022_PATH=C:\Program Files\Microsoft Visual Studio\2022"
set "MSBUILD_PATH="

REM Try to find MSBuild in different VS2022 editions
if exist "%VS2022_PATH%\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    set "MSBUILD_PATH=%VS2022_PATH%\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "%VS2022_PATH%\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set "MSBUILD_PATH=%VS2022_PATH%\Professional\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "%VS2022_PATH%\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set "MSBUILD_PATH=%VS2022_PATH%\Community\MSBuild\Current\Bin\MSBuild.exe"
)

if "%MSBUILD_PATH%"=="" (
    echo ERROR: Visual Studio 2022 MSBuild not found!
    echo Please install Visual Studio 2022 or update the paths in this script.
    echo.
    echo Expected locations:
    echo - %VS2022_PATH%\Enterprise\MSBuild\Current\Bin\MSBuild.exe
    echo - %VS2022_PATH%\Professional\MSBuild\Current\Bin\MSBuild.exe
    echo - %VS2022_PATH%\Community\MSBuild\Current\Bin\MSBuild.exe
    echo.
    pause
    exit /b 1
)

echo Found MSBuild: %MSBUILD_PATH%
echo.

REM Create necessary directories
echo Creating directories...
if not exist "bin" mkdir bin
if not exist "obj" mkdir obj
if not exist "logs" mkdir logs
if not exist "config" mkdir config
echo.

REM Build Debug x64 configuration
echo Building Debug x64 configuration...
echo ----------------------------------------
"%MSBUILD_PATH%" RFOnlineServer.sln /p:Configuration=Debug /p:Platform=x64 /verbosity:minimal

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERROR: Debug x64 build failed!
    echo Check the build output above for errors.
    echo.
    pause
    exit /b 1
)

echo.
echo Debug x64 build successful!
echo.

REM Build Release x64 configuration
echo Building Release x64 configuration...
echo ----------------------------------------
"%MSBUILD_PATH%" RFOnlineServer.sln /p:Configuration=Release /p:Platform=x64 /verbosity:minimal

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERROR: Release x64 build failed!
    echo Check the build output above for errors.
    echo.
    pause
    exit /b 1
)

echo.
echo Release x64 build successful!
echo.

REM Show build results
echo ========================================
echo Build Results:
echo ========================================
if exist "bin\x64\Debug\RFOnlineServer.exe" (
    echo [OK] Debug x64 executable created
    for %%I in ("bin\x64\Debug\RFOnlineServer.exe") do echo     Size: %%~zI bytes
) else (
    echo [FAIL] Debug x64 executable not found
)

if exist "bin\x64\Release\RFOnlineServer.exe" (
    echo [OK] Release x64 executable created
    for %%I in ("bin\x64\Release\RFOnlineServer.exe") do echo     Size: %%~zI bytes
) else (
    echo [FAIL] Release x64 executable not found
)

echo.
echo ========================================
echo Build test completed successfully!
echo ========================================
echo.
echo You can now:
echo 1. Open RFOnlineServer.sln in Visual Studio 2022
echo 2. Run the Debug version: bin\x64\Debug\RFOnlineServer.exe
echo 3. Run the Release version: bin\x64\Release\RFOnlineServer.exe
echo.
pause
