/**
 * @file rf_server.h
 * @brief Main include header for RF Online Server
 * @details Central header that includes all major subsystems and modules
 * 
 * This header provides a convenient way to include all the major components
 * of the RF Online Server system. Include this file to get access to all
 * core functionality.
 * 
 * <AUTHOR> Online Server Refactoring Project
 * @date 2025
 */

#ifndef RF_SERVER_H
#define RF_SERVER_H

// =============================================================================
// Core Type Definitions and Constants
// =============================================================================

#include "rf_types.h"
#include "rf_constants.h"
#include "rf_structures.h"

// =============================================================================
// Game Data and Records
// =============================================================================

#include "monster_records.h"

// =============================================================================
// World and Character Systems
// =============================================================================

#include "../src/world/character.h"
#include "../src/world/monster.h"

// =============================================================================
// Logging and System Utilities
// =============================================================================

#include "../src/logging/log_file.h"
#include "../src/logging/async_log_info.h"
#include "../src/system/timer.h"

// =============================================================================
// Version Information
// =============================================================================

#define RF_SERVER_VERSION_MAJOR     1
#define RF_SERVER_VERSION_MINOR     0
#define RF_SERVER_VERSION_PATCH     0
#define RF_SERVER_VERSION_BUILD     1

#define RF_SERVER_VERSION_STRING    "1.0.0.1"
#define RF_SERVER_BUILD_DATE        __DATE__
#define RF_SERVER_BUILD_TIME        __TIME__

// =============================================================================
// Compiler and Platform Detection
// =============================================================================

// Detect compiler
#if defined(_MSC_VER)
    #define RF_COMPILER_MSVC
    #define RF_COMPILER_VERSION _MSC_VER
#elif defined(__GNUC__)
    #define RF_COMPILER_GCC
    #define RF_COMPILER_VERSION (__GNUC__ * 10000 + __GNUC_MINOR__ * 100 + __GNUC_PATCHLEVEL__)
#elif defined(__clang__)
    #define RF_COMPILER_CLANG
    #define RF_COMPILER_VERSION (__clang_major__ * 10000 + __clang_minor__ * 100 + __clang_patchlevel__)
#endif

// Detect platform
#if defined(_WIN32) || defined(_WIN64)
    #define RF_PLATFORM_WINDOWS
    #if defined(_WIN64)
        #define RF_PLATFORM_64BIT
    #else
        #define RF_PLATFORM_32BIT
    #endif
#elif defined(__linux__)
    #define RF_PLATFORM_LINUX
    #if defined(__x86_64__) || defined(__amd64__)
        #define RF_PLATFORM_64BIT
    #else
        #define RF_PLATFORM_32BIT
    #endif
#endif

// =============================================================================
// Build Configuration
// =============================================================================

#if defined(_DEBUG) || defined(DEBUG)
    #define RF_DEBUG_BUILD
#else
    #define RF_RELEASE_BUILD
#endif

// =============================================================================
// API Macros
// =============================================================================

#ifdef RF_COMPILER_MSVC
    #define RF_FORCE_INLINE     __forceinline
    #define RF_NO_INLINE        __declspec(noinline)
    #define RF_ALIGN(x)         __declspec(align(x))
    #define RF_DEPRECATED       __declspec(deprecated)
    #define RF_EXPORT           __declspec(dllexport)
    #define RF_IMPORT           __declspec(dllimport)
#else
    #define RF_FORCE_INLINE     inline __attribute__((always_inline))
    #define RF_NO_INLINE        __attribute__((noinline))
    #define RF_ALIGN(x)         __attribute__((aligned(x)))
    #define RF_DEPRECATED       __attribute__((deprecated))
    #define RF_EXPORT           __attribute__((visibility("default")))
    #define RF_IMPORT
#endif

// =============================================================================
// Debug and Assertion Macros
// =============================================================================

#ifdef RF_DEBUG_BUILD
    #include <cassert>
    #define RF_ASSERT(condition)        assert(condition)
    #define RF_ASSERT_MSG(condition, msg) assert((condition) && (msg))
    #define RF_DEBUG_ONLY(code)         code
#else
    #define RF_ASSERT(condition)        ((void)0)
    #define RF_ASSERT_MSG(condition, msg) ((void)0)
    #define RF_DEBUG_ONLY(code)         ((void)0)
#endif

// =============================================================================
// Memory Management Macros
// =============================================================================

#define RF_SAFE_DELETE(ptr) \
    do { \
        if (ptr) { \
            delete (ptr); \
            (ptr) = nullptr; \
        } \
    } while(0)

#define RF_SAFE_DELETE_ARRAY(ptr) \
    do { \
        if (ptr) { \
            delete[] (ptr); \
            (ptr) = nullptr; \
        } \
    } while(0)

#define RF_SAFE_RELEASE(ptr) \
    do { \
        if (ptr) { \
            (ptr)->Release(); \
            (ptr) = nullptr; \
        } \
    } while(0)

// =============================================================================
// Utility Macros
// =============================================================================

#define RF_UNUSED(x)                ((void)(x))
#define RF_STRINGIFY(x)             #x
#define RF_CONCAT(a, b)             a##b
#define RF_UNIQUE_NAME(prefix)      RF_CONCAT(prefix, __LINE__)

// Bit manipulation
#define RF_BIT(n)                   (1U << (n))
#define RF_SET_FLAG(flags, flag)    ((flags) |= (flag))
#define RF_CLEAR_FLAG(flags, flag)  ((flags) &= ~(flag))
#define RF_TEST_FLAG(flags, flag)   (((flags) & (flag)) != 0)
#define RF_TOGGLE_FLAG(flags, flag) ((flags) ^= (flag))

// Min/Max macros (if not already defined)
#ifndef RF_MIN
    #define RF_MIN(a, b)            (((a) < (b)) ? (a) : (b))
#endif
#ifndef RF_MAX
    #define RF_MAX(a, b)            (((a) > (b)) ? (a) : (b))
#endif

// Clamp macro
#define RF_CLAMP(value, min_val, max_val) \
    RF_MAX((min_val), RF_MIN((value), (max_val)))

// =============================================================================
// Error Handling
// =============================================================================

/**
 * @brief Error handling callback function type
 * @param error_code Error code
 * @param error_message Error message
 * @param user_data User-provided data
 */
typedef void (*ErrorCallback)(ResultCode error_code, const char* error_message, void* user_data);

/**
 * @brief Set global error callback
 * @param callback Callback function to set
 * @param user_data User data to pass to callback
 */
void SetErrorCallback(ErrorCallback callback, void* user_data = nullptr);

/**
 * @brief Report an error
 * @param error_code Error code
 * @param error_message Error message
 */
void ReportError(ResultCode error_code, const char* error_message);

// =============================================================================
// Initialization and Cleanup
// =============================================================================

/**
 * @brief Initialize the RF Server system
 * @param config_file Path to configuration file (optional)
 * @return RESULT_SUCCESS if initialization successful, error code otherwise
 */
ResultCode InitializeRFServer(const char* config_file = nullptr);

/**
 * @brief Shutdown the RF Server system
 * @details Cleans up all allocated resources and shuts down subsystems
 */
void ShutdownRFServer();

/**
 * @brief Check if RF Server is initialized
 * @return true if initialized, false otherwise
 */
bool IsRFServerInitialized();

// =============================================================================
// System Information
// =============================================================================

/**
 * @brief Get RF Server version string
 * @return Version string
 */
const char* GetRFServerVersion();

/**
 * @brief Get RF Server build information
 * @param build_date Output for build date
 * @param build_time Output for build time
 */
void GetRFServerBuildInfo(const char** build_date, const char** build_time);

/**
 * @brief Get system information
 * @param platform Output for platform name
 * @param architecture Output for architecture (32/64 bit)
 * @param compiler Output for compiler name
 */
void GetSystemInfo(const char** platform, const char** architecture, const char** compiler);

// =============================================================================
// Namespace for C++ Code
// =============================================================================

#ifdef __cplusplus
namespace RFServer {
    // Forward declarations for main subsystem classes
    class ServerCore;
    class WorldManager;
    class PlayerManager;
    class MonsterManager;
    class DatabaseManager;
    class NetworkManager;
    class LoggingManager;
    
    /**
     * @brief Get the main server core instance
     * @return Pointer to server core (nullptr if not initialized)
     */
    ServerCore* GetServerCore();
    
    /**
     * @brief Initialize all C++ subsystems
     * @return RESULT_SUCCESS if successful, error code otherwise
     */
    ResultCode InitializeCppSystems();
    
    /**
     * @brief Shutdown all C++ subsystems
     */
    void ShutdownCppSystems();
}
#endif // __cplusplus

// =============================================================================
// Feature Flags
// =============================================================================

// Enable/disable specific features during compilation
#define RF_FEATURE_LOGGING          1
#define RF_FEATURE_MONSTER_AI       1
#define RF_FEATURE_PLAYER_SYSTEM    1
#define RF_FEATURE_DATABASE         1
#define RF_FEATURE_NETWORKING       1
#define RF_FEATURE_LUA_SCRIPTING    0  // Disabled for now
#define RF_FEATURE_PHYSICS          0  // Disabled for now
#define RF_FEATURE_AUDIO            0  // Disabled for now

// =============================================================================
// Configuration Constants
// =============================================================================

// Default configuration values
#define RF_DEFAULT_CONFIG_FILE      "rf_server.cfg"
#define RF_DEFAULT_LOG_DIRECTORY    "logs"
#define RF_DEFAULT_DATA_DIRECTORY   "data"
#define RF_DEFAULT_SCRIPT_DIRECTORY "scripts"

// Performance tuning
#define RF_DEFAULT_THREAD_POOL_SIZE 4
#define RF_DEFAULT_NETWORK_BUFFER_SIZE 8192
#define RF_DEFAULT_DATABASE_POOL_SIZE 10

#endif // RF_SERVER_H
