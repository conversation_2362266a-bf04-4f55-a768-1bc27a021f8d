/**
 * @file looting_manager.h
 * @brief Loot management system for RF Online Server
 * @details Refactored from CLootingMgr - manages monster loot drops and distribution
 * 
 * Original IDA Pro functions:
 * - CLootingMgr::Init
 * - CLootingMgr related functions
 * 
 * <AUTHOR> Online Server Refactoring Project
 * @date 2025
 */

#ifndef LOOTING_MANAGER_H
#define LOOTING_MANAGER_H

#include "../../include/rf_types.h"
#include "../../include/rf_constants.h"
#include <vector>
#include <memory>
#include <mutex>

// =============================================================================
// Forward Declarations
// =============================================================================

class Character;
class Monster;
class ItemInstance;

// =============================================================================
// Loot Entry Structure
// =============================================================================

/**
 * @brief Individual loot entry
 */
struct LootEntry {
    UInt32 item_id;                 ///< Item template ID
    UInt16 quantity;                ///< Quantity to drop
    float drop_chance;              ///< Drop chance (0.0 to 1.0)
    Level min_level;                ///< Minimum level requirement
    Level max_level;                ///< Maximum level requirement
    bool is_rare;                   ///< Whether this is a rare drop
    bool is_quest_item;             ///< Whether this is a quest item
    
    LootEntry()
        : item_id(0)
        , quantity(1)
        , drop_chance(0.0f)
        , min_level(1)
        , max_level(MAX_LEVEL)
        , is_rare(false)
        , is_quest_item(false)
    {}
};

// =============================================================================
// Loot Table Structure
// =============================================================================

/**
 * @brief Loot table containing multiple loot entries
 */
struct LootTable {
    UInt32 table_id;                ///< Unique table identifier
    std::vector<LootEntry> entries; ///< Loot entries in this table
    UInt32 max_drops;               ///< Maximum number of items to drop
    float gold_drop_chance;         ///< Chance to drop gold
    UInt32 min_gold;                ///< Minimum gold amount
    UInt32 max_gold;                ///< Maximum gold amount
    
    LootTable()
        : table_id(0)
        , max_drops(3)
        , gold_drop_chance(0.8f)
        , min_gold(1)
        , max_gold(10)
    {}
};

// =============================================================================
// Looter Information
// =============================================================================

/**
 * @brief Information about a character who can loot
 */
struct LooterInfo {
    Character* character;           ///< Character who can loot
    CharacterSerial character_serial; ///< Character serial for validation
    Int32 damage_contribution;     ///< Damage contributed to kill
    float loot_bonus;              ///< Loot bonus multiplier
    bool has_looting_rights;       ///< Whether character has looting rights
    GameTime loot_expire_time;     ///< When looting rights expire
    
    LooterInfo()
        : character(nullptr)
        , character_serial(INVALID_ID)
        , damage_contribution(0)
        , loot_bonus(1.0f)
        , has_looting_rights(false)
        , loot_expire_time(0)
    {}
};

// =============================================================================
// Generated Loot
// =============================================================================

/**
 * @brief Generated loot item
 */
struct GeneratedLoot {
    std::unique_ptr<ItemInstance> item; ///< Generated item instance
    Character* reserved_for;            ///< Character this loot is reserved for (nullptr = anyone)
    GameTime expire_time;               ///< When this loot expires
    bool is_picked_up;                  ///< Whether loot has been picked up
    
    GeneratedLoot()
        : reserved_for(nullptr)
        , expire_time(0)
        , is_picked_up(false)
    {}
};

// =============================================================================
// LootingManager Class
// =============================================================================

/**
 * @brief Manages loot generation and distribution
 * @details Handles loot table processing, drop generation, and loot rights
 * 
 * This class replaces the original CLootingMgr and provides:
 * - Configurable loot tables
 * - Fair loot distribution
 * - Loot rights management
 * - Automatic loot cleanup
 */
class LootingManager {
public:
    // =========================================================================
    // Constructor and Destructor
    // =========================================================================
    
    /**
     * @brief Constructor
     */
    LootingManager();
    
    /**
     * @brief Destructor
     */
    ~LootingManager();
    
    // Disable copy constructor and assignment operator
    LootingManager(const LootingManager&) = delete;
    LootingManager& operator=(const LootingManager&) = delete;
    
    // =========================================================================
    // Initialization
    // =========================================================================
    
    /**
     * @brief Initialize the looting manager
     * @param owner Monster that owns this looting manager
     * @param max_looters Maximum number of looters to track
     * @return true if initialization successful, false otherwise
     * 
     * Replaces: CLootingMgr::Init
     */
    bool Initialize(Monster* owner, UInt32 max_looters = 16);
    
    /**
     * @brief Shutdown the looting manager
     */
    void Shutdown();
    
    /**
     * @brief Check if looting manager is initialized
     * @return true if initialized, false otherwise
     */
    bool IsInitialized() const { return is_initialized_; }
    
    // =========================================================================
    // Looter Management
    // =========================================================================
    
    /**
     * @brief Add a potential looter
     * @param character Character to add as looter
     * @param damage_contribution Damage contributed by this character
     */
    void AddLooter(Character* character, Int32 damage_contribution);
    
    /**
     * @brief Remove a looter
     * @param character Character to remove
     */
    void RemoveLooter(Character* character);
    
    /**
     * @brief Check if character has looting rights
     * @param character Character to check
     * @return true if has looting rights, false otherwise
     */
    bool HasLootingRights(Character* character) const;
    
    /**
     * @brief Get number of looters
     * @return Number of characters with looting rights
     */
    size_t GetLooterCount() const { return looters_.size(); }
    
    // =========================================================================
    // Loot Generation
    // =========================================================================
    
    /**
     * @brief Generate loot from monster's loot table
     * @param killer Character who killed the monster (gets priority)
     */
    void GenerateLoot(Character* killer);
    
    /**
     * @brief Generate loot from specific loot table
     * @param loot_table Loot table to use
     * @param killer Character who killed the monster
     */
    void GenerateLootFromTable(const LootTable& loot_table, Character* killer);
    
    /**
     * @brief Get generated loot items
     * @return Vector of generated loot
     */
    const std::vector<GeneratedLoot>& GetGeneratedLoot() const { return generated_loot_; }
    
    /**
     * @brief Check if loot has been generated
     * @return true if loot generated, false otherwise
     */
    bool IsLootGenerated() const { return loot_generated_; }
    
    // =========================================================================
    // Loot Pickup
    // =========================================================================
    
    /**
     * @brief Attempt to pick up loot
     * @param character Character attempting to pick up loot
     * @param loot_index Index of loot item to pick up
     * @return true if pickup successful, false otherwise
     */
    bool PickupLoot(Character* character, size_t loot_index);
    
    /**
     * @brief Check if character can pick up specific loot
     * @param character Character to check
     * @param loot_index Index of loot item
     * @return true if can pick up, false otherwise
     */
    bool CanPickupLoot(Character* character, size_t loot_index) const;
    
    /**
     * @brief Get loot that character can pick up
     * @param character Character to check for
     * @return Vector of loot indices that character can pick up
     */
    std::vector<size_t> GetAvailableLoot(Character* character) const;
    
    // =========================================================================
    // Loot Rights Management
    // =========================================================================
    
    /**
     * @brief Calculate loot rights based on damage contribution
     */
    void CalculateLootRights();
    
    /**
     * @brief Set loot rights duration
     * @param duration_ms Duration in milliseconds
     */
    void SetLootRightsDuration(GameTime duration_ms) { loot_rights_duration_ = duration_ms; }
    
    /**
     * @brief Get loot rights duration
     * @return Duration in milliseconds
     */
    GameTime GetLootRightsDuration() const { return loot_rights_duration_; }
    
    // =========================================================================
    // Update and Cleanup
    // =========================================================================
    
    /**
     * @brief Update looting manager
     * @param delta_time Time elapsed since last update (milliseconds)
     */
    void Update(UInt32 delta_time);
    
    /**
     * @brief Clean up expired loot
     */
    void CleanupExpiredLoot();
    
    /**
     * @brief Clear all loot and looters
     */
    void Clear();

private:
    // =========================================================================
    // Private Methods
    // =========================================================================
    
    /**
     * @brief Find looter info for character
     * @param character Character to find
     * @return Iterator to looter info (end() if not found)
     */
    std::vector<LooterInfo>::iterator FindLooter(Character* character);
    
    /**
     * @brief Find looter info for character (const version)
     * @param character Character to find
     * @return Const iterator to looter info (end() if not found)
     */
    std::vector<LooterInfo>::const_iterator FindLooter(Character* character) const;
    
    /**
     * @brief Roll for loot drop
     * @param entry Loot entry to roll for
     * @param killer Character who killed the monster
     * @return true if item should drop, false otherwise
     */
    bool RollForDrop(const LootEntry& entry, Character* killer) const;
    
    /**
     * @brief Create item instance from loot entry
     * @param entry Loot entry to create item from
     * @return Unique pointer to created item (nullptr if creation failed)
     */
    std::unique_ptr<ItemInstance> CreateItemFromEntry(const LootEntry& entry) const;
    
    /**
     * @brief Determine loot reservation
     * @param killer Character who killed the monster
     * @return Character to reserve loot for (nullptr = no reservation)
     */
    Character* DetermineLootReservation(Character* killer) const;
    
    /**
     * @brief Validate character pointer
     * @param character Character to validate
     * @return true if valid, false otherwise
     */
    bool IsValidCharacter(Character* character) const;
    
    // =========================================================================
    // Member Variables
    // =========================================================================
    
    Monster* owner_;                        ///< Monster that owns this looting manager
    bool is_initialized_;                   ///< Initialization status
    bool loot_generated_;                   ///< Whether loot has been generated
    
    // Looter tracking
    std::vector<LooterInfo> looters_;       ///< Characters with looting rights
    UInt32 max_looters_;                    ///< Maximum number of looters
    
    // Generated loot
    std::vector<GeneratedLoot> generated_loot_; ///< Generated loot items
    
    // Configuration
    GameTime loot_rights_duration_;         ///< How long loot rights last
    GameTime loot_expire_time_;             ///< How long loot stays on ground
    
    // Thread safety
    mutable std::mutex loot_mutex_;         ///< Mutex for thread safety
    
    // Constants
    static constexpr GameTime DEFAULT_LOOT_RIGHTS_DURATION = 60000;  ///< 1 minute
    static constexpr GameTime DEFAULT_LOOT_EXPIRE_TIME = 300000;     ///< 5 minutes
    static constexpr float DAMAGE_CONTRIBUTION_THRESHOLD = 0.05f;    ///< 5% damage for loot rights
};

#endif // LOOTING_MANAGER_H
