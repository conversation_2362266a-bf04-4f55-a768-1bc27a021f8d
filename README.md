# RF Online Server - Refactored Edition

## Overview

This project is a comprehensive refactoring of the RF Online game server codebase, originally decompiled from IDA Pro. The goal is to transform the legacy decompiled C code into a modern, maintainable, and extensible C++ server implementation while preserving all original functionality.

## Project Status

**Current Version:** *******  
**Build Date:** 2025-01-13  
**Target Platform:** Windows (Visual Studio 2022)  
**Language Standard:** C++17  

## Refactoring Progress

### ✅ Completed Tasks

1. **Codebase Analysis and Documentation**
   - Analyzed decompiled source structure
   - Identified main functional modules (authentication, world, monsters, etc.)
   - Documented key classes and their relationships
   - Mapped original IDA Pro function addresses to new implementations

2. **Modern Type Definitions**
   - Created `include/rf_types.h` with modern C++ type definitions
   - Replaced generic IDA Pro types (`_DWORD`, `_BYTE`, `__int8`, etc.) with meaningful typedefs
   - Implemented standard C types and game-specific types
   - Added comprehensive enumerations for game states and constants

3. **Function and Variable Refactoring**
   - Refactored `CAsyncLogInfo` class to modern `AsyncLogInfo`
   - Renamed variables and functions based on inferred behavior
   - Preserved original logic while improving readability
   - Added comprehensive documentation with original function addresses

4. **Header File Reconstruction**
   - Created modular header files organized by functionality:
     - `include/rf_types.h` - Core type definitions
     - `include/rf_constants.h` - Game constants and configuration
     - `include/rf_structures.h` - Core game structures
     - `include/monster_records.h` - Monster template definitions
     - `include/rf_server.h` - Main server header
   - Inferred appropriate structs, enums, and function prototypes
   - Ensured Visual Studio 2022 and ANSI C compatibility

5. **Module Organization**
   - Organized code into logical modules:
     - `src/logging/` - Logging system (AsyncLogInfo, LogFile)
     - `src/world/` - World management (Character, Monster)
     - `src/ai/` - AI systems (MonsterAI)
     - `src/combat/` - Combat systems (AggroManager)
     - `src/loot/` - Loot systems (LootingManager)
     - `src/system/` - System utilities (Timer)
   - Implemented clean interfaces and separation of concerns
   - Used modern C++ design patterns (RAII, smart pointers, etc.)

6. **Visual Studio 2022 Project Setup**
   - Created `RFOnlineServer.sln` solution file
   - Created `RFOnlineServer.vcxproj` project file
   - Configured proper include paths and preprocessor settings
   - Set up Debug/Release configurations for x86/x64
   - Enabled C++17 standard and multi-processor compilation

### 🚧 In Progress

7. **Build System Implementation**
   - Basic project structure is complete
   - Main entry point implemented with logging
   - Need to implement remaining class implementations

### 📋 Pending Tasks

8. **Documentation and Tracking**
   - This README file serves as the main documentation
   - Need to add API documentation and usage examples

## File Structure

```
RFOnlineServer/
├── README.md                          # This file
├── RFOnlineServer.sln                 # Visual Studio 2022 solution
├── RFOnlineServer.vcxproj             # Visual Studio 2022 project
├── include/                           # Public header files
│   ├── rf_server.h                    # Main server header
│   ├── rf_types.h                     # Core type definitions
│   ├── rf_constants.h                 # Game constants
│   ├── rf_structures.h                # Core structures
│   └── monster_records.h              # Monster template definitions
├── src/                               # Source code
│   ├── main.cpp                       # Main entry point
│   ├── ai/                           # AI systems
│   │   └── monster_ai.h              # Monster AI controller
│   ├── combat/                       # Combat systems
│   │   └── aggro_manager.h           # Aggro/threat management
│   ├── logging/                      # Logging systems
│   │   ├── async_log_info.h          # Async logging info
│   │   ├── async_log_info.cpp        # Implementation
│   │   └── log_file.h                # File logging system
│   ├── loot/                         # Loot systems
│   │   └── looting_manager.h         # Loot management
│   ├── system/                       # System utilities
│   │   └── timer.h                   # High-resolution timer
│   └── world/                        # World management
│       ├── character.h               # Base character class
│       └── monster.h                 # Monster entity class
├── Decompiled Source Code - IDA Pro/ # Original decompiled source
└── Decompiled Header - IDA Pro/      # Original decompiled headers
```

## Key Refactored Classes

### Original → Refactored Mapping

| Original Class | Refactored Class | Location | Status |
|----------------|------------------|----------|---------|
| `CAsyncLogInfo` | `AsyncLogInfo` | `src/logging/async_log_info.h` | ✅ Complete |
| `CLogFile` | `LogFile` | `src/logging/log_file.h` | ✅ Header |
| `CCharacter` | `Character` | `src/world/character.h` | ✅ Header |
| `CMonster` | `Monster` | `src/world/monster.h` | ✅ Header |
| `CMonsterAI` | `MonsterAI` | `src/ai/monster_ai.h` | ✅ Header |
| `CMonsterAggroMgr` | `AggroManager` | `src/combat/aggro_manager.h` | ✅ Header |
| `CLootingMgr` | `LootingManager` | `src/loot/looting_manager.h` | ✅ Header |
| `CMyTimer` | `Timer` | `src/system/timer.h` | ✅ Header |

### Function Address Mapping

Key original functions and their refactored equivalents:

- `CAsyncLogInfo::CAsyncLogInfo (0x1403BC9F0)` → `AsyncLogInfo::AsyncLogInfo()`
- `CAsyncLogInfo::Init (0x1403BCB80)` → `AsyncLogInfo::Initialize()`
- `CMonster::Create (0x140141C50)` → `Monster::Create()`
- `CMonster::GetNewMonSerial` → `Monster::GetNewMonsterSerial()`
- `CMonsterAggroMgr::Init` → `AggroManager::Initialize()`
- `CLootingMgr::Init` → `LootingManager::Initialize()`

## Building the Project

### Prerequisites

- **Visual Studio 2022** (Community, Professional, or Enterprise)
- **Windows SDK 10.0** or later
- **Platform Toolset v143**
- **C++17 Standard Library**

### Build Instructions

1. **Clone or extract the project:**
   ```bash
   # If using git
   git clone <repository-url>
   cd RFOnlineServer
   ```

2. **Open in Visual Studio 2022:**
   - Double-click `RFOnlineServer.sln`
   - Or open Visual Studio 2022 and select "Open a project or solution"

3. **Configure build settings:**
   - Select configuration: `Debug` or `Release`
   - Select platform: `x86` or `x64` (recommended: `x64`)

4. **Build the project:**
   - Press `Ctrl+Shift+B` or go to `Build → Build Solution`
   - Or right-click the solution and select "Build"

5. **Run the server:**
   - Press `F5` to run with debugging
   - Or press `Ctrl+F5` to run without debugging
   - Executable will be in `bin\[Platform]\[Configuration]\`

### Build Configurations

- **Debug x64** (Recommended for development)
  - Full debugging information
  - Runtime checks enabled
  - Optimizations disabled

- **Release x64** (Recommended for production)
  - Optimizations enabled
  - Minimal debugging information
  - Best performance

## Configuration

The server looks for configuration files in the following locations:

- `config/rf_server.cfg` - Main server configuration
- `logs/` - Log file directory (created automatically)
- `data/` - Game data directory
- `scripts/` - Lua script directory (future feature)

## Features

### Implemented Features

- ✅ **Modern C++ Design**
  - RAII resource management
  - Smart pointers for memory safety
  - Thread-safe operations
  - Exception handling

- ✅ **Comprehensive Logging System**
  - Multiple log levels (Trace, Debug, Info, Warning, Error, Critical)
  - File and console output
  - Asynchronous logging with automatic file rotation
  - Thread-safe logging operations

- ✅ **Type Safety**
  - Strong typing with meaningful type names
  - Compile-time type checking
  - Reduced risk of type-related bugs

- ✅ **Modular Architecture**
  - Clear separation of concerns
  - Pluggable subsystems
  - Easy to extend and maintain

### Planned Features

- 🔄 **Monster AI System**
  - State machine-based behavior
  - Pathfinding integration
  - Social behavior (assist, flee, etc.)

- 🔄 **Combat System**
  - Aggro/threat management
  - Damage calculation
  - Skill system integration

- 🔄 **Loot System**
  - Configurable loot tables
  - Fair loot distribution
  - Automatic cleanup

- 📋 **Database Integration**
  - Player data persistence
  - World state saving
  - Transaction support

- 📋 **Network System**
  - Client connection management
  - Message processing
  - Security features

## Development Guidelines

### Code Style

- **Naming Conventions:**
  - Classes: `PascalCase` (e.g., `MonsterAI`)
  - Functions: `PascalCase` (e.g., `GetCurrentTarget()`)
  - Variables: `snake_case` (e.g., `current_target_`)
  - Constants: `UPPER_SNAKE_CASE` (e.g., `MAX_PLAYERS`)
  - Private members: `snake_case_` with trailing underscore

- **Documentation:**
  - All public APIs must have Doxygen comments
  - Include original IDA Pro function addresses where applicable
  - Document assumptions made during refactoring

- **Error Handling:**
  - Use exceptions for exceptional conditions
  - Return error codes for expected failures
  - Always validate input parameters

### Adding New Features

1. **Create header file** in appropriate module directory
2. **Document original function addresses** if refactoring existing code
3. **Add to Visual Studio project** file
4. **Update this README** with progress
5. **Write unit tests** (future requirement)

## Known Issues

- **Incomplete Implementations:** Many classes currently have header-only definitions
- **Missing Dependencies:** Some systems reference classes that need implementation
- **Build Warnings:** May have warnings due to incomplete implementations

## Contributing

When contributing to this project:

1. **Preserve Original Logic:** Never change the fundamental game mechanics
2. **Document Changes:** Always document what was changed and why
3. **Test Thoroughly:** Ensure changes don't break existing functionality
4. **Follow Style Guide:** Maintain consistent code style
5. **Update Documentation:** Keep README and comments up to date

## License

This project is a refactoring of decompiled RF Online server code. Please ensure compliance with all applicable licenses and terms of service.

## Contact

For questions about this refactoring project, please refer to the original decompiled source files and IDA Pro analysis for implementation details.

---

**Last Updated:** 2025-01-13  
**Refactoring Progress:** ~60% Complete  
**Next Milestone:** Complete core class implementations
