/*
 * Function: _CryptoPP::DL_PrivateKeyImpl_CryptoPP::DL_GroupParameters_EC_CryptoPP::ECP___::Validate_::_1_::dtor$0
 * Address: 0x140451850
 */

void __fastcall CryptoPP::DL_PrivateKeyImpl_CryptoPP::DL_GroupParameters_EC_CryptoPP::ECP___::Validate_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 104) & 1 )
  {
    *(_DWORD *)(a2 + 104) &= 0xFFFFFFFE;
    CryptoPP::Integer::~Integer((CryptoPP::Integer *)(a2 + 64));
  }
}
