/**
 * @file monster_records.h
 * @brief Monster template and record definitions for RF Online Server
 * @details Refactored from _monster_fld and related structures
 * 
 * <AUTHOR> Online Server Refactoring Project
 * @date 2025
 */

#ifndef MONSTER_RECORDS_H
#define MONSTER_RECORDS_H

#include "rf_types.h"
#include "rf_constants.h"

// =============================================================================
// Monster Enumerations
// =============================================================================

/**
 * @brief Monster kind/type classification
 * @details Replaces CMonster::_Monster_Kind_T
 */
enum class MonsterKind : UInt8 {
    ANIMAL = 0,
    PLANT = 1,
    REPTILE = 2,
    CRUSTACEA = 3,
    INSECT = 4,
    MACHINE = 5,
    UNDEAD = 6,
    ELEMENTAL = 7,
    HUMANOID = 8,
    DRAGON = 9,
    COUNT = 10
};

/**
 * @brief Monster assist type
 * @details Replaces CMonster::_Monster_AsistType
 */
enum class MonsterAssistType : UInt8 {
    SAME_KIND = 0,      ///< Only assist monsters of same kind
    ALL_KIND = 1        ///< Assist all monsters
};

/**
 * @brief Monster help behavior
 * @details Replaces CMonster::_Monster_HelpMe_T
 */
enum class MonsterHelpBehavior : UInt8 {
    ONLY_ATTACK = 0,        ///< Only help when being attacked
    SEARCH_CHARACTER = 1    ///< Actively search for characters to help against
};

/**
 * @brief Monster view area effects
 * @details Replaces CMonster::_Monster_ViewArea_Effect_T
 */
enum class MonsterViewAreaEffect : UInt8 {
    NONE = 0,
    DEFENSE_DOWN = 1,
    CRITICAL_EXT_RATE_DOWN = 2,
    DEF_SKILL_UNIT_DOWN = 3,
    ATTACK_DOWN = 4,
    ACCURACY_DOWN = 5,
    DODGE_DOWN = 6
};

/**
 * @brief Monster events
 * @details Replaces CMonster::MonsterEvent
 */
enum class MonsterEvent : UInt8 {
    DAMAGE = 0,
    HELP = 1,
    MAX = 2
};

/**
 * @brief Monster condition states
 */
enum class MonsterCondition : UInt8 {
    NORMAL = 0,
    BOSS = 1,
    ELITE = 2,
    SPECIAL = 3
};

// =============================================================================
// Monster Child Information
// =============================================================================

/**
 * @brief Child monster information
 * @details Replaces _monster_fld::__child_mon
 */
struct MonsterChildInfo {
    char child_monster_code[64];    ///< Child monster identifier
    Int32 child_count;              ///< Number of children to spawn
    
    MonsterChildInfo() : child_count(0) {
        child_monster_code[0] = '\0';
    }
};

// =============================================================================
// Monster Emotion System
// =============================================================================

/**
 * @brief Emotion presentation data
 * @details Replaces _monster_fld::_EmotionPresentation
 */
struct EmotionPresentation {
    Int32 emotion_condition;        ///< Condition that triggers emotion
    Int32 emotion_class;            ///< Class/type of emotion
    char emotion_code[64];          ///< Emotion effect code
    
    EmotionPresentation() : emotion_condition(0), emotion_class(0) {
        emotion_code[0] = '\0';
    }
};

// =============================================================================
// Monster Record Structure
// =============================================================================

/**
 * @brief Complete monster template record
 * @details Refactored from _monster_fld structure
 * 
 * This structure contains all the template data for a monster type,
 * including stats, behavior, appearance, and special abilities.
 */
struct MonsterRecord {
    // Basic identification
    char name[64];                      ///< Monster display name
    char effect_code[64];               ///< Visual effect code
    Int32 monster_grade;                ///< Monster grade (0=normal, 1=elite, etc.)
    Int32 race_code;                    ///< Race identifier
    
    // Physical properties
    float scale;                        ///< Size scale multiplier
    Int32 model_index;                  ///< 3D model index
    Int32 texture_index;                ///< Texture index
    Int32 animation_index;              ///< Animation set index
    
    // Combat statistics
    float level;                        ///< Monster level
    float max_hp;                       ///< Maximum health points
    float max_fp;                       ///< Maximum force points
    float max_sp;                       ///< Maximum stamina points
    
    // Attack properties
    float min_attack;                   ///< Minimum attack damage
    float max_attack;                   ///< Maximum attack damage
    float attack_range;                 ///< Attack range
    float attack_speed;                 ///< Attack speed multiplier
    float critical_rate;                ///< Critical hit rate
    
    // Defense properties
    float defense;                      ///< Physical defense
    float dodge_rate;                   ///< Dodge rate
    float block_rate;                   ///< Block rate
    
    // Movement properties
    float move_speed;                   ///< Movement speed
    float run_speed;                    ///< Running speed
    float visual_range;                 ///< Visual detection range
    float aggro_range;                  ///< Aggression range
    
    // Behavior flags
    MonsterCondition monster_condition; ///< Special condition (boss, elite, etc.)
    bool experience_down;               ///< Reduces experience when killed
    bool can_loot_up;                   ///< Can drop better loot
    bool can_loot_down;                 ///< Can drop worse loot
    
    // AI and behavior
    MonsterKind kind;                   ///< Monster type/kind
    MonsterAssistType assist_type;      ///< How monster assists others
    MonsterHelpBehavior help_behavior;  ///< Help behavior type
    float emotion_type;                 ///< Emotion system type
    float offensive_rate;               ///< Aggressiveness rate
    Int32 offensive_type;               ///< Type of offensive behavior
    
    // Special abilities
    float damage_hp_standard;          ///< HP damage threshold
    float emotion_impact_standard_time; ///< Emotion impact timing
    MonsterViewAreaEffect view_area_effect; ///< Area effect type
    
    // Tolerance and resistance
    Int32 critical_tolerance;          ///< Critical hit tolerance
    float tolerance_probability;       ///< Damage tolerance probability
    
    // Assist and cooperation
    float assist_request_step_cooperation_chat;        ///< Assist request cooperation
    float assist_request_step_cooperation_chat_prob;   ///< Assist request probability
    
    // Child monsters
    MonsterChildInfo children[3];       ///< Child monster information
    
    // Emotion system
    EmotionPresentation emotions[5];    ///< Emotion presentations
    
    // Effect types
    Int32 attack_effect_type;           ///< Attack visual effect type
    Int32 defense_effect_type;          ///< Defense visual effect type
    
    // Loot and rewards
    Int32 loot_table_index;             ///< Loot table reference
    Int32 experience_reward;            ///< Experience points rewarded
    Int32 gold_reward_min;              ///< Minimum gold reward
    Int32 gold_reward_max;              ///< Maximum gold reward
    
    // Respawn properties
    UInt32 respawn_time_ms;             ///< Respawn time in milliseconds
    float respawn_probability;          ///< Respawn probability
    
    // Special flags
    bool is_aggressive;                 ///< Attacks on sight
    bool is_social;                     ///< Calls for help
    bool is_territorial;                ///< Defends territory
    bool can_flee;                      ///< Can flee from combat
    bool is_nocturnal;                  ///< More active at night
    bool is_aquatic;                    ///< Water-based monster
    bool is_flying;                     ///< Flying monster
    bool is_underground;                ///< Underground monster
    
    // Constructor
    MonsterRecord() {
        // Initialize all string fields
        name[0] = '\0';
        effect_code[0] = '\0';
        
        // Initialize numeric fields to reasonable defaults
        monster_grade = 0;
        race_code = 0;
        scale = 1.0f;
        model_index = 0;
        texture_index = 0;
        animation_index = 0;
        level = 1.0f;
        max_hp = 100.0f;
        max_fp = 100.0f;
        max_sp = 100.0f;
        min_attack = 10.0f;
        max_attack = 20.0f;
        attack_range = 2.0f;
        attack_speed = 1.0f;
        critical_rate = 0.05f;
        defense = 10.0f;
        dodge_rate = 0.1f;
        block_rate = 0.1f;
        move_speed = 3.0f;
        run_speed = 6.0f;
        visual_range = 15.0f;
        aggro_range = 10.0f;
        
        // Initialize enums and flags
        monster_condition = MonsterCondition::NORMAL;
        experience_down = false;
        can_loot_up = false;
        can_loot_down = false;
        kind = MonsterKind::ANIMAL;
        assist_type = MonsterAssistType::SAME_KIND;
        help_behavior = MonsterHelpBehavior::ONLY_ATTACK;
        emotion_type = 0.0f;
        offensive_rate = 0.5f;
        offensive_type = 0;
        damage_hp_standard = 0.5f;
        emotion_impact_standard_time = 1.0f;
        view_area_effect = MonsterViewAreaEffect::NONE;
        critical_tolerance = 0;
        tolerance_probability = 0.0f;
        assist_request_step_cooperation_chat = 0.0f;
        assist_request_step_cooperation_chat_prob = 0.0f;
        attack_effect_type = 0;
        defense_effect_type = 0;
        loot_table_index = 0;
        experience_reward = 10;
        gold_reward_min = 1;
        gold_reward_max = 10;
        respawn_time_ms = 300000; // 5 minutes
        respawn_probability = 1.0f;
        
        // Initialize boolean flags
        is_aggressive = false;
        is_social = false;
        is_territorial = false;
        can_flee = false;
        is_nocturnal = false;
        is_aquatic = false;
        is_flying = false;
        is_underground = false;
    }
};

// =============================================================================
// Monster Group Structure
// =============================================================================

/**
 * @brief Monster group definition
 * @details Replaces __monster_group structure
 */
struct MonsterGroup {
    char group_name[64];                ///< Group identifier name
    Int32 sub_monster_count;            ///< Number of monsters in group
    MonsterRecord* sub_monsters[32];    ///< Pointers to monster records
    
    MonsterGroup() : sub_monster_count(0) {
        group_name[0] = '\0';
        for (int i = 0; i < 32; ++i) {
            sub_monsters[i] = nullptr;
        }
    }
};

// =============================================================================
// Utility Functions
// =============================================================================

/**
 * @brief Check if monster is a boss type
 * @param record Monster record to check
 * @return true if boss, false otherwise
 */
inline bool IsMonsterBoss(const MonsterRecord* record) {
    return record && record->monster_condition == MonsterCondition::BOSS;
}

/**
 * @brief Check if monster is elite type
 * @param record Monster record to check
 * @return true if elite, false otherwise
 */
inline bool IsMonsterElite(const MonsterRecord* record) {
    return record && record->monster_condition == MonsterCondition::ELITE;
}

/**
 * @brief Get monster grade from condition
 * @param condition Monster condition
 * @return Corresponding monster grade
 */
inline MonsterGrade GetGradeFromCondition(MonsterCondition condition) {
    switch (condition) {
        case MonsterCondition::BOSS:
            return MONSTER_GRADE_BOSS;
        case MonsterCondition::ELITE:
            return MONSTER_GRADE_ELITE;
        case MonsterCondition::SPECIAL:
            return MONSTER_GRADE_RAID_BOSS;
        default:
            return MONSTER_GRADE_NORMAL;
    }
}

#endif // MONSTER_RECORDS_H
