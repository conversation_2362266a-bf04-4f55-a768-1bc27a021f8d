/*
 * Function: ?OnRecvSession@HACKSHEILD_PARAM_ANTICP@@UEAA_NPEAVCHackShieldExSystem@@HE_KPEAD@Z
 * Address: 0x140417F10
 */

bool __fastcall HACKSHEILD_PARAM_ANTICP::OnRecvSession(HACKSHEILD_PARAM_ANTICP *this, CHackShieldExSystem *mgr, int nIndex, char byProtocol, unsigned __int64 tSize, char *pMsg)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@7
  __int64 v9; // [sp+0h] [bp-38h]@1
  char v10; // [sp+20h] [bp-18h]@4
  HACKSHEILD_PARAM_ANTICP *v11; // [sp+40h] [bp+8h]@1

  v11 = this;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v10 = byProtocol;
  if ( byProtocol == 1 )
  {
    result = HACKSHEILD_PARAM_ANTICP::OnRecvSession_ServerCheckSum_Request(v11, nIndex);
  }
  else if ( v10 == 3 )
  {
    result = HACKSHEILD_PARAM_ANTICP::OnRecvSession_ClientCheckSum_Response(v11, tSize, pMsg);
  }
  else if ( v10 == 5 )
  {
    result = HACKSHEILD_PARAM_ANTICP::OnRecvSession_ClientCrc_Response(v11, tSize, pMsg);
  }
  else
  {
    result = 0;
  }
  return result;
}
