/**
 * @file aggro_manager.h
 * @brief Aggro (threat) management system for RF Online Server
 * @details Refactored from CMonsterAggroMgr - manages monster threat tables
 * 
 * Original IDA Pro functions:
 * - CMonsterAggroMgr::Init
 * - CMonsterAggroMgr related functions
 * 
 * <AUTHOR> Online Server Refactoring Project
 * @date 2025
 */

#ifndef AGGRO_MANAGER_H
#define AGGRO_MANAGER_H

#include "../../include/rf_types.h"
#include "../../include/rf_constants.h"
#include <vector>
#include <mutex>

// =============================================================================
// Forward Declarations
// =============================================================================

class Character;
class Monster;

// =============================================================================
// Aggro Node Structure
// =============================================================================

/**
 * @brief Individual aggro entry for a character
 * @details Replaces CAggroNode from original code
 */
struct AggroNode {
    Character* character;           ///< Character this aggro is for
    CharacterSerial character_serial; ///< Character serial for validation
    Int32 aggro_value;             ///< Current aggro/threat value
    Int32 damage_value;            ///< Total damage dealt
    GameTime last_update_time;     ///< Last time this entry was updated
    bool is_active;                ///< Whether this entry is active
    
    AggroNode() 
        : character(nullptr)
        , character_serial(INVALID_ID)
        , aggro_value(0)
        , damage_value(0)
        , last_update_time(0)
        , is_active(false)
    {}
};

// =============================================================================
// AggroManager Class
// =============================================================================

/**
 * @brief Manages monster aggro/threat system
 * @details Tracks which characters have generated threat with a monster
 * 
 * This class replaces the original CMonsterAggroMgr and provides:
 * - Thread-safe aggro tracking
 * - Automatic aggro decay over time
 * - Top aggro/damage tracking
 * - Efficient aggro list management
 */
class AggroManager {
public:
    // =========================================================================
    // Constructor and Destructor
    // =========================================================================
    
    /**
     * @brief Constructor
     */
    AggroManager();
    
    /**
     * @brief Destructor
     */
    ~AggroManager();
    
    // Disable copy constructor and assignment operator
    AggroManager(const AggroManager&) = delete;
    AggroManager& operator=(const AggroManager&) = delete;
    
    // =========================================================================
    // Initialization
    // =========================================================================
    
    /**
     * @brief Initialize the aggro manager
     * @param owner Monster that owns this aggro manager
     * @return true if initialization successful, false otherwise
     * 
     * Replaces: CMonsterAggroMgr::Init
     */
    bool Initialize(Monster* owner);
    
    /**
     * @brief Shutdown the aggro manager
     */
    void Shutdown();
    
    /**
     * @brief Check if aggro manager is initialized
     * @return true if initialized, false otherwise
     */
    bool IsInitialized() const { return is_initialized_; }
    
    // =========================================================================
    // Aggro Management
    // =========================================================================
    
    /**
     * @brief Add aggro for a character
     * @param character Character to add aggro for
     * @param aggro_amount Amount of aggro to add
     */
    void AddAggro(Character* character, Int32 aggro_amount);
    
    /**
     * @brief Add damage for a character
     * @param character Character to add damage for
     * @param damage_amount Amount of damage to add
     */
    void AddDamage(Character* character, Int32 damage_amount);
    
    /**
     * @brief Remove aggro for a character
     * @param character Character to remove aggro for
     */
    void RemoveAggro(Character* character);
    
    /**
     * @brief Clear all aggro
     */
    void ClearAllAggro();
    
    /**
     * @brief Get aggro value for a character
     * @param character Character to check
     * @return Aggro value (0 if not found)
     */
    Int32 GetAggro(Character* character) const;
    
    /**
     * @brief Get damage value for a character
     * @param character Character to check
     * @return Damage value (0 if not found)
     */
    Int32 GetDamage(Character* character) const;
    
    // =========================================================================
    // Top Aggro Tracking
    // =========================================================================
    
    /**
     * @brief Get character with highest aggro
     * @return Pointer to top aggro character (nullptr if none)
     */
    Character* GetTopAggroCharacter() const { return top_aggro_character_; }
    
    /**
     * @brief Get character with highest damage
     * @return Pointer to top damage character (nullptr if none)
     */
    Character* GetTopDamageCharacter() const { return top_damage_character_; }
    
    /**
     * @brief Get character with king power damage
     * @return Pointer to king power damage character (nullptr if none)
     */
    Character* GetKingPowerDamageCharacter() const { return king_power_damage_character_; }
    
    /**
     * @brief Get top aggro value
     * @return Highest aggro value
     */
    Int32 GetTopAggroValue() const;
    
    /**
     * @brief Get top damage value
     * @return Highest damage value
     */
    Int32 GetTopDamageValue() const;
    
    // =========================================================================
    // Aggro List Access
    // =========================================================================
    
    /**
     * @brief Get number of characters in aggro list
     * @return Number of characters with aggro
     */
    size_t GetAggroCount() const;
    
    /**
     * @brief Get aggro list (read-only)
     * @return Vector of aggro nodes
     */
    const std::vector<AggroNode>& GetAggroList() const { return aggro_list_; }
    
    /**
     * @brief Check if character is in aggro list
     * @param character Character to check
     * @return true if in aggro list, false otherwise
     */
    bool HasAggro(Character* character) const;
    
    // =========================================================================
    // Update and Maintenance
    // =========================================================================
    
    /**
     * @brief Update aggro manager
     * @param delta_time Time elapsed since last update (milliseconds)
     */
    void Update(UInt32 delta_time);
    
    /**
     * @brief Force update of top aggro/damage characters
     */
    void UpdateTopCharacters();
    
    /**
     * @brief Clean up invalid entries
     */
    void CleanupInvalidEntries();

private:
    // =========================================================================
    // Private Methods
    // =========================================================================
    
    /**
     * @brief Find aggro node for character
     * @param character Character to find
     * @return Iterator to aggro node (end() if not found)
     */
    std::vector<AggroNode>::iterator FindAggroNode(Character* character);
    
    /**
     * @brief Find aggro node for character (const version)
     * @param character Character to find
     * @return Const iterator to aggro node (end() if not found)
     */
    std::vector<AggroNode>::const_iterator FindAggroNode(Character* character) const;
    
    /**
     * @brief Get or create aggro node for character
     * @param character Character to get/create node for
     * @return Reference to aggro node
     */
    AggroNode& GetOrCreateAggroNode(Character* character);
    
    /**
     * @brief Update top aggro character
     */
    void UpdateTopAggro();
    
    /**
     * @brief Update top damage character
     */
    void UpdateTopDamage();
    
    /**
     * @brief Apply aggro decay
     * @param delta_time Time elapsed since last update
     */
    void ApplyAggroDecay(UInt32 delta_time);
    
    /**
     * @brief Validate character pointer
     * @param character Character to validate
     * @return true if valid, false otherwise
     */
    bool IsValidCharacter(Character* character) const;
    
    // =========================================================================
    // Member Variables
    // =========================================================================
    
    Monster* owner_;                        ///< Monster that owns this aggro manager
    bool is_initialized_;                   ///< Initialization status
    
    // Aggro tracking
    std::vector<AggroNode> aggro_list_;     ///< List of aggro entries
    Character* top_aggro_character_;        ///< Character with highest aggro
    Character* top_damage_character_;       ///< Character with highest damage
    Character* king_power_damage_character_; ///< Character with king power damage
    
    // Timing
    GameTime last_update_time_;             ///< Last update time
    GameTime all_reset_timer_;              ///< Timer for full aggro reset
    GameTime short_rank_timer_;             ///< Timer for short ranking updates
    
    // Thread safety
    mutable std::mutex aggro_mutex_;        ///< Mutex for thread safety
    
    // Configuration
    static constexpr size_t MAX_AGGRO_ENTRIES = 10;        ///< Maximum aggro entries
    static constexpr Int32 AGGRO_DECAY_RATE = 1;           ///< Aggro decay per second
    static constexpr GameTime AGGRO_DECAY_INTERVAL = 1000; ///< Aggro decay interval (1 second)
    static constexpr GameTime ALL_RESET_INTERVAL = 300000; ///< Full reset interval (5 minutes)
    static constexpr GameTime SHORT_RANK_INTERVAL = 5000;  ///< Short ranking interval (5 seconds)
    static constexpr Int32 MIN_AGGRO_VALUE = 1;            ///< Minimum aggro to keep entry
};

#endif // AGGRO_MANAGER_H
