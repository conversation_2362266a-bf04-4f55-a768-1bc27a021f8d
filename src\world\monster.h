/**
 * @file monster.h
 * @brief Monster entity management for RF Online Server
 * @details Refactored from CMonster class - handles monster AI, behavior, and lifecycle
 * 
 * Original IDA Pro functions:
 * - CMonster::Create (0x140141C50)
 * - CMonster::GetNewMonSerial
 * - CMonster::GetMonsterGrade
 * - CMonster::SetDefPart
 * - CMonster::CreateAI
 * - CMonster::SetMoveType
 * - CMonster::CheckMonsterStateData
 * - CMonster::SendMsg_Create
 * - CMonster::UpdateLookAtPos
 * - CMonster::GetVisualField
 * 
 * <AUTHOR> Online Server Refactoring Project
 * @date 2025
 */

#ifndef MONSTER_H
#define MONSTER_H

#include "../../include/rf_types.h"
#include "../../include/rf_constants.h"
#include "../../include/rf_structures.h"
#include "character.h"
#include "../ai/monster_ai.h"
#include "../combat/aggro_manager.h"
#include "../loot/looting_manager.h"
#include <memory>
#include <vector>

// =============================================================================
// Forward Declarations
// =============================================================================

class MonsterRecord;
class ActiveRecord;
class DummyPosition;
class EventSet;
class MonsterSkillPool;
class LuaSignalReactor;
class EmotionPresentationChecker;
class SFContDamageTolerance;
class MonsterHierarchy;

// =============================================================================
// Monster Creation Data
// =============================================================================

/**
 * @brief Data structure for monster creation
 * @details Replaces _monster_create_setdata from original code
 */
struct MonsterCreateData {
    MonsterRecord* monster_record;          ///< Monster template data
    Position3D start_position;              ///< Initial spawn position
    ActiveRecord* active_record;            ///< Active spawn record (optional)
    DummyPosition* dummy_position;          ///< Dummy position data (optional)
    EventSet* event_set;                    ///< Event configuration (optional)
    Monster* parent_monster;                ///< Parent monster for hierarchies (optional)
    bool rob_experience;                    ///< Whether monster can rob experience
    bool reward_experience;                 ///< Whether monster rewards experience
    bool is_dungeon_monster;                ///< Whether this is a dungeon monster
    
    MonsterCreateData()
        : monster_record(nullptr)
        , start_position{0.0f, 0.0f, 0.0f}
        , active_record(nullptr)
        , dummy_position(nullptr)
        , event_set(nullptr)
        , parent_monster(nullptr)
        , rob_experience(false)
        , reward_experience(true)
        , is_dungeon_monster(false)
    {}
};

// =============================================================================
// Monster Class
// =============================================================================

/**
 * @brief Monster entity class
 * @details Manages monster behavior, AI, combat, and lifecycle
 * 
 * This class replaces the original CMonster and provides:
 * - Modern C++ design with RAII
 * - Thread-safe operations where needed
 * - Clear separation of concerns
 * - Comprehensive monster management
 */
class Monster : public Character {
public:
    // =========================================================================
    // Constructor and Destructor
    // =========================================================================
    
    /**
     * @brief Default constructor
     */
    Monster();
    
    /**
     * @brief Destructor
     * @details Cleans up all allocated resources
     */
    virtual ~Monster();
    
    // Disable copy constructor and assignment operator
    Monster(const Monster&) = delete;
    Monster& operator=(const Monster&) = delete;
    
    // =========================================================================
    // Creation and Initialization
    // =========================================================================
    
    /**
     * @brief Create and initialize monster
     * @param create_data Monster creation parameters
     * @return true if creation successful, false otherwise
     * 
     * Replaces: CMonster::Create (0x140141C50)
     */
    bool Create(const MonsterCreateData& create_data);
    
    /**
     * @brief Destroy the monster
     * @details Cleans up resources and removes from world
     */
    void Destroy();
    
    /**
     * @brief Check if monster is properly initialized
     * @return true if initialized, false otherwise
     */
    bool IsInitialized() const { return is_initialized_; }
    
    // =========================================================================
    // Monster Properties
    // =========================================================================
    
    /**
     * @brief Get monster serial number
     * @return Unique monster serial
     */
    ObjectSerial GetSerial() const { return monster_serial_; }
    
    /**
     * @brief Get monster template record
     * @return Pointer to monster record data
     */
    const MonsterRecord* GetMonsterRecord() const { return monster_record_; }
    
    /**
     * @brief Get monster grade
     * @return Monster grade (normal, elite, boss, etc.)
     * 
     * Replaces: CMonster::GetMonsterGrade
     */
    MonsterGrade GetGrade() const;
    
    /**
     * @brief Get current monster state
     * @return Current state (idle, combat, dead, etc.)
     */
    MonsterState GetState() const { return current_state_; }
    
    /**
     * @brief Set monster state
     * @param new_state New state to set
     */
    void SetState(MonsterState new_state);
    
    // =========================================================================
    // Position and Movement
    // =========================================================================
    
    /**
     * @brief Get spawn position
     * @return Original spawn position
     */
    const Position3D& GetSpawnPosition() const { return spawn_position_; }
    
    /**
     * @brief Get target position
     * @return Current movement target position
     */
    const Position3D& GetTargetPosition() const { return target_position_; }
    
    /**
     * @brief Set target position
     * @param position New target position
     */
    void SetTargetPosition(const Position3D& position);
    
    /**
     * @brief Get look-at position
     * @return Position monster is looking at
     */
    const Position3D& GetLookAtPosition() const { return look_at_position_; }
    
    /**
     * @brief Update look-at position
     * @param position New look-at position
     * 
     * Replaces: CMonster::UpdateLookAtPos
     */
    void UpdateLookAtPosition(const Position3D& position);
    
    /**
     * @brief Set movement type
     * @param move_type Type of movement behavior
     * 
     * Replaces: CMonster::SetMoveType
     */
    void SetMovementType(UInt32 move_type);
    
    // =========================================================================
    // Combat and Health
    // =========================================================================
    
    /**
     * @brief Get current health points
     * @return Current HP
     */
    Int32 GetCurrentHP() const { return current_hp_; }
    
    /**
     * @brief Set current health points
     * @param hp New HP value
     */
    void SetCurrentHP(Int32 hp);
    
    /**
     * @brief Get maximum health points
     * @return Maximum HP
     */
    Int32 GetMaxHP() const;
    
    /**
     * @brief Check if monster is alive
     * @return true if alive, false if dead
     */
    bool IsAlive() const { return current_hp_ > 0; }
    
    /**
     * @brief Get aggro manager
     * @return Reference to aggro management system
     */
    AggroManager& GetAggroManager() { return aggro_manager_; }
    const AggroManager& GetAggroManager() const { return aggro_manager_; }
    
    // =========================================================================
    // AI and Behavior
    // =========================================================================
    
    /**
     * @brief Create and initialize AI
     * @param ai_type Type of AI to create
     * @return true if AI created successfully, false otherwise
     * 
     * Replaces: CMonster::CreateAI
     */
    bool CreateAI(UInt32 ai_type);
    
    /**
     * @brief Get AI controller
     * @return Pointer to AI controller (may be null)
     */
    MonsterAI* GetAI() { return ai_controller_.get(); }
    const MonsterAI* GetAI() const { return ai_controller_.get(); }
    
    /**
     * @brief Update monster logic
     * @param delta_time Time elapsed since last update (milliseconds)
     */
    void Update(UInt32 delta_time);
    
    /**
     * @brief Get visual field range
     * @return Visual detection range
     * 
     * Replaces: CMonster::GetVisualField
     */
    float GetVisualField() const;
    
    // =========================================================================
    // Loot and Rewards
    // =========================================================================
    
    /**
     * @brief Get looting manager
     * @return Reference to loot management system
     */
    LootingManager& GetLootingManager() { return looting_manager_; }
    const LootingManager& GetLootingManager() const { return looting_manager_; }
    
    /**
     * @brief Check if monster can rob experience
     * @return true if can rob experience, false otherwise
     */
    bool CanRobExperience() const { return can_rob_experience_; }
    
    /**
     * @brief Check if monster rewards experience
     * @return true if rewards experience, false otherwise
     */
    bool RewardsExperience() const { return rewards_experience_; }
    
    // =========================================================================
    // Events and Hierarchy
    // =========================================================================
    
    /**
     * @brief Get monster hierarchy manager
     * @return Reference to hierarchy system
     */
    MonsterHierarchy& GetHierarchy() { return hierarchy_; }
    const MonsterHierarchy& GetHierarchy() const { return hierarchy_; }
    
    /**
     * @brief Check if this is a boss monster
     * @return true if boss, false otherwise
     */
    bool IsBoss() const;
    
    /**
     * @brief Get creation date/time
     * @return Array containing [month, day, hour, minute]
     */
    const UInt8* GetCreationDate() const { return creation_date_; }
    
    // =========================================================================
    // Static Methods
    // =========================================================================
    
    /**
     * @brief Get new unique monster serial
     * @return New unique serial number
     * 
     * Replaces: CMonster::GetNewMonSerial
     */
    static ObjectSerial GetNewMonsterSerial();
    
    /**
     * @brief Get total number of live monsters
     * @return Count of active monsters
     */
    static UInt32 GetLiveMonsterCount() { return live_monster_count_; }

protected:
    // =========================================================================
    // Protected Methods
    // =========================================================================
    
    /**
     * @brief Initialize default parts/equipment
     * @param monster_record Monster template data
     * 
     * Replaces: CMonster::SetDefPart
     */
    void InitializeDefaultParts(const MonsterRecord* monster_record);
    
    /**
     * @brief Check and validate monster state data
     * 
     * Replaces: CMonster::CheckMonsterStateData
     */
    void ValidateStateData();
    
    /**
     * @brief Send monster creation message to clients
     * 
     * Replaces: CMonster::SendMsg_Create
     */
    void SendCreationMessage();
    
    /**
     * @brief Write boss birth log entry
     */
    void WriteBossBirthLog();

private:
    // =========================================================================
    // Member Variables
    // =========================================================================
    
    // Core monster data
    ObjectSerial monster_serial_;               ///< Unique monster identifier
    MonsterRecord* monster_record_;             ///< Monster template data
    MonsterState current_state_;                ///< Current monster state
    bool is_initialized_;                       ///< Initialization status
    
    // Position and movement
    Position3D spawn_position_;                 ///< Original spawn position
    Position3D target_position_;                ///< Current movement target
    Position3D look_at_position_;               ///< Current look-at position
    Position3D start_look_at_position_;         ///< Initial look-at position
    bool should_rotate_;                        ///< Whether monster should rotate
    
    // Health and combat
    Int32 current_hp_;                          ///< Current health points
    GameTime last_recovery_time_;               ///< Last HP recovery time
    GameTime destroy_time_;                     ///< Scheduled destruction time
    
    // Behavior flags
    bool can_rob_experience_;                   ///< Can rob experience from players
    bool rewards_experience_;                   ///< Rewards experience when killed
    bool use_standard_loot_;                    ///< Uses standard loot tables
    bool is_dungeon_monster_;                   ///< Is a dungeon monster
    bool is_apparition_;                        ///< Is an apparition/ghost
    bool is_operational_;                       ///< Is currently operational
    
    // Lifecycle management
    GameTime life_cycle_start_;                 ///< When monster was created
    GameTime life_cycle_max_;                   ///< Maximum lifetime
    UInt8 creation_date_[4];                    ///< Creation date [month, day, hour, minute]
    
    // Associated objects
    ActiveRecord* active_record_;               ///< Active spawn record
    DummyPosition* dummy_position_;             ///< Dummy position data
    EventSet* event_set_;                       ///< Event configuration
    
    // Management systems
    std::unique_ptr<MonsterAI> ai_controller_;  ///< AI controller
    AggroManager aggro_manager_;                ///< Aggro management
    LootingManager looting_manager_;            ///< Loot management
    MonsterHierarchy hierarchy_;                ///< Hierarchy management
    
    // Additional systems (placeholders for now)
    std::unique_ptr<MonsterSkillPool> skill_pool_;              ///< Skill management
    std::unique_ptr<LuaSignalReactor> lua_reactor_;             ///< Lua scripting
    std::unique_ptr<EmotionPresentationChecker> emotion_checker_; ///< Emotion system
    std::unique_ptr<SFContDamageTolerance> damage_tolerance_;   ///< Damage tolerance
    
    // Static data
    static std::atomic<ObjectSerial> next_serial_;  ///< Next available serial number
    static std::atomic<UInt32> live_monster_count_;  ///< Count of live monsters
};

#endif // MONSTER_H
