/*
 * Function: ?ValidateParameters@ECP@CryptoPP@@QEBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x14060E2A0
 */

char __fastcall CryptoPP::ECP::ValidateParameters(CryptoPP::ECP *this, struct CryptoPP::RandomNumberGenerator *a2, unsigned int a3)
{
  unsigned int v3; // er9@1
  CryptoPP::Integer b; // [sp+20h] [bp-248h]@1
  char v6; // [sp+48h] [bp-220h]@1
  CryptoPP::Integer a; // [sp+50h] [bp-218h]@10
  CryptoPP::Integer result; // [sp+78h] [bp-1F0h]@10
  CryptoPP::Integer v9; // [sp+A0h] [bp-1C8h]@10
  CryptoPP::Integer v10; // [sp+C8h] [bp-1A0h]@10
  CryptoPP::Integer v11; // [sp+F0h] [bp-178h]@10
  CryptoPP::Integer v12; // [sp+118h] [bp-150h]@10
  CryptoPP::Integer v13; // [sp+140h] [bp-128h]@10
  CryptoPP::Integer v14; // [sp+168h] [bp-100h]@10
  CryptoPP::Integer v15; // [sp+190h] [bp-D8h]@10
  char v16; // [sp+1B8h] [bp-B0h]@37
  int v17; // [sp+1BCh] [bp-ACh]@1
  __int64 v18; // [sp+1C0h] [bp-A8h]@1
  int v19; // [sp+1C8h] [bp-A0h]@6
  CryptoPP::Integer *v20; // [sp+1D0h] [bp-98h]@10
  CryptoPP::Integer *v21; // [sp+1D8h] [bp-90h]@10
  CryptoPP::Integer *v22; // [sp+1E0h] [bp-88h]@10
  CryptoPP::Integer *v23; // [sp+1E8h] [bp-80h]@10
  const struct CryptoPP::Integer *v24; // [sp+1F0h] [bp-78h]@10
  CryptoPP::Integer *v25; // [sp+1F8h] [bp-70h]@10
  CryptoPP::Integer *v26; // [sp+200h] [bp-68h]@10
  CryptoPP::Integer *v27; // [sp+208h] [bp-60h]@10
  CryptoPP::Integer *v28; // [sp+210h] [bp-58h]@10
  CryptoPP::Integer *v29; // [sp+218h] [bp-50h]@10
  CryptoPP::Integer *v30; // [sp+220h] [bp-48h]@10
  CryptoPP::Integer *v31; // [sp+228h] [bp-40h]@10
  CryptoPP::Integer *v32; // [sp+230h] [bp-38h]@10
  CryptoPP::Integer *v33; // [sp+238h] [bp-30h]@10
  CryptoPP::Integer *v34; // [sp+240h] [bp-28h]@10
  CryptoPP::Integer *v35; // [sp+248h] [bp-20h]@10
  CryptoPP::Integer *v36; // [sp+250h] [bp-18h]@10
  int v37; // [sp+258h] [bp-10h]@11
  int v38; // [sp+25Ch] [bp-Ch]@34
  CryptoPP::ECP *v39; // [sp+270h] [bp+8h]@1
  CryptoPP *v40; // [sp+278h] [bp+10h]@1
  unsigned int v41; // [sp+280h] [bp+18h]@1

  v41 = a3;
  v40 = (CryptoPP *)a2;
  v39 = this;
  v18 = -2i64;
  v17 = 0;
  CryptoPP::ECP::FieldSize(this, &b);
  v6 = CryptoPP::Integer::IsOdd(&b);
  v19 = v6
     && !CryptoPP::Integer::IsNegative(&v39->m_a)
     && CryptoPP::operator<(&v39->m_a, &b)
     && !CryptoPP::Integer::IsNegative(&v39->m_b)
     && CryptoPP::operator<(&v39->m_b, &b);
  v6 = v19;
  if ( v41 >= 1 )
  {
    v37 = v6
       && (CryptoPP::Integer::Integer(&a, 27),
           v17 |= 1u,
           CryptoPP::Integer::Integer(&v10, 4),
           v17 |= 2u,
           v20 = &v39->m_b,
           v21 = CryptoPP::operator*(&result, &a, &v39->m_b),
           v22 = v21,
           v17 |= 4u,
           v23 = CryptoPP::operator*(&v9, v21, v20),
           v24 = v23,
           v17 |= 8u,
           v25 = &v39->m_a,
           v26 = &v39->m_a,
           v27 = CryptoPP::operator*(&v11, &v10, &v39->m_a),
           v28 = v27,
           v17 |= 0x10u,
           v29 = CryptoPP::operator*(&v12, v27, v26),
           v30 = v29,
           v17 |= 0x20u,
           v31 = CryptoPP::operator*(&v13, v29, v25),
           v32 = v31,
           v17 |= 0x40u,
           v33 = CryptoPP::operator+(&v14, v31, v24),
           v34 = v33,
           v17 |= 0x80u,
           v35 = CryptoPP::operator%(&v15, v33, &b),
           v36 = v35,
           v17 |= 0x100u,
           CryptoPP::Integer::IsPositive(v35));
    v6 = v37;
    if ( v17 & 0x100 )
    {
      v17 &= 0xFFFFFEFF;
      CryptoPP::Integer::~Integer(&v15);
    }
    if ( v17 & 0x80 )
    {
      v17 &= 0xFFFFFF7F;
      CryptoPP::Integer::~Integer(&v14);
    }
    if ( v17 & 0x40 )
    {
      v17 &= 0xFFFFFFBF;
      CryptoPP::Integer::~Integer(&v13);
    }
    if ( v17 & 0x20 )
    {
      v17 &= 0xFFFFFFDF;
      CryptoPP::Integer::~Integer(&v12);
    }
    if ( v17 & 0x10 )
    {
      v17 &= 0xFFFFFFEF;
      CryptoPP::Integer::~Integer(&v11);
    }
    if ( v17 & 8 )
    {
      v17 &= 0xFFFFFFF7;
      CryptoPP::Integer::~Integer(&v9);
    }
    if ( v17 & 4 )
    {
      v17 &= 0xFFFFFFFB;
      CryptoPP::Integer::~Integer(&result);
    }
    if ( v17 & 2 )
    {
      v17 &= 0xFFFFFFFD;
      CryptoPP::Integer::~Integer(&v10);
    }
    if ( v17 & 1 )
    {
      v17 &= 0xFFFFFFFE;
      CryptoPP::Integer::~Integer(&a);
    }
  }
  if ( v41 >= 2 )
  {
    v38 = v6
       && CryptoPP::VerifyPrime(
            v40,
            (struct CryptoPP::RandomNumberGenerator *)&b,
            (const struct CryptoPP::Integer *)1,
            v3);
    v6 = v38;
  }
  v16 = v6;
  CryptoPP::Integer::~Integer(&b);
  return v16;
}
