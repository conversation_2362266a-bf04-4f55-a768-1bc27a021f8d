/**
 * @file timer.h
 * @brief Modern timer implementation for RF Online Server
 * @details Replaces CMyTimer from original codebase with modern C++ implementation
 * 
 * <AUTHOR> Online Server Refactoring Project
 * @date 2025
 */

#ifndef TIMER_H
#define TIMER_H

#include "../../include/rf_types.h"
#include <chrono>
#include <functional>
#include <thread>
#include <atomic>
#include <mutex>

// =============================================================================
// Timer Class
// =============================================================================

/**
 * @brief High-resolution timer with callback support
 * @details Modern replacement for CMyTimer providing:
 * - High-resolution timing using std::chrono
 * - Thread-safe operations
 * - One-shot and repeating timer modes
 * - Callback function support
 * - Proper RAII resource management
 */
class Timer {
public:
    // =========================================================================
    // Type Definitions
    // =========================================================================
    
    using TimerCallback = std::function<void()>;
    using TimePoint = std::chrono::steady_clock::time_point;
    using Duration = std::chrono::milliseconds;
    
    // =========================================================================
    // Constructor and Destructor
    // =========================================================================
    
    /**
     * @brief Default constructor
     */
    Timer();
    
    /**
     * @brief Constructor with callback
     * @param callback Function to call when timer expires
     */
    explicit Timer(TimerCallback callback);
    
    /**
     * @brief Destructor
     * @details Automatically stops the timer and cleans up resources
     */
    ~Timer();
    
    // Disable copy constructor and assignment operator
    Timer(const Timer&) = delete;
    Timer& operator=(const Timer&) = delete;
    
    // Enable move constructor and assignment operator
    Timer(Timer&& other) noexcept;
    Timer& operator=(Timer&& other) noexcept;
    
    // =========================================================================
    // Timer Control
    // =========================================================================
    
    /**
     * @brief Start the timer
     * @param interval_ms Timer interval in milliseconds
     * @param repeating Whether timer should repeat (true) or fire once (false)
     * @return true if timer started successfully, false otherwise
     */
    bool Start(UInt32 interval_ms, bool repeating = false);
    
    /**
     * @brief Stop the timer
     * @details Stops the timer and waits for any running callback to complete
     */
    void Stop();
    
    /**
     * @brief Reset the timer
     * @details Restarts the timer with the same interval and mode
     * @return true if reset successful, false if timer wasn't running
     */
    bool Reset();
    
    /**
     * @brief Pause the timer
     * @details Pauses the timer, preserving remaining time
     */
    void Pause();
    
    /**
     * @brief Resume a paused timer
     * @details Resumes the timer from where it was paused
     */
    void Resume();
    
    // =========================================================================
    // Status and Information
    // =========================================================================
    
    /**
     * @brief Check if timer is currently running
     * @return true if running, false otherwise
     */
    bool IsRunning() const;
    
    /**
     * @brief Check if timer is paused
     * @return true if paused, false otherwise
     */
    bool IsPaused() const;
    
    /**
     * @brief Check if timer is in repeating mode
     * @return true if repeating, false if one-shot
     */
    bool IsRepeating() const;
    
    /**
     * @brief Get the timer interval
     * @return Timer interval in milliseconds
     */
    UInt32 GetInterval() const;
    
    /**
     * @brief Get elapsed time since timer started
     * @return Elapsed time in milliseconds
     */
    UInt32 GetElapsedTime() const;
    
    /**
     * @brief Get remaining time until timer expires
     * @return Remaining time in milliseconds (0 if expired or not running)
     */
    UInt32 GetRemainingTime() const;
    
    // =========================================================================
    // Callback Management
    // =========================================================================
    
    /**
     * @brief Set the callback function
     * @param callback Function to call when timer expires
     */
    void SetCallback(TimerCallback callback);
    
    /**
     * @brief Clear the callback function
     */
    void ClearCallback();
    
    /**
     * @brief Check if timer has a callback set
     * @return true if callback is set, false otherwise
     */
    bool HasCallback() const;

private:
    // =========================================================================
    // Private Methods
    // =========================================================================
    
    /**
     * @brief Timer thread function
     * @details Runs in a separate thread to handle timer logic
     */
    void TimerThreadFunction();
    
    /**
     * @brief Calculate next expiration time
     * @return Time point when timer should next expire
     */
    TimePoint CalculateNextExpiration() const;
    
    /**
     * @brief Stop the timer thread
     * @details Internal method to stop and join the timer thread
     */
    void StopTimerThread();
    
    // =========================================================================
    // Member Variables
    // =========================================================================
    
    TimerCallback callback_;                    ///< Callback function
    Duration interval_;                         ///< Timer interval
    TimePoint start_time_;                      ///< When timer was started
    TimePoint pause_time_;                      ///< When timer was paused
    Duration paused_duration_;                  ///< Total time spent paused
    
    std::atomic<bool> is_running_;              ///< Timer running state
    std::atomic<bool> is_paused_;               ///< Timer paused state
    std::atomic<bool> is_repeating_;            ///< Timer repeating mode
    std::atomic<bool> should_stop_;             ///< Signal to stop timer thread
    
    std::unique_ptr<std::thread> timer_thread_; ///< Timer thread
    mutable std::mutex mutex_;                  ///< Mutex for thread safety
    
    // Constants
    static constexpr UInt32 MIN_INTERVAL_MS = 1;       ///< Minimum timer interval
    static constexpr UInt32 MAX_INTERVAL_MS = 86400000; ///< Maximum timer interval (24 hours)
};

// =============================================================================
// Utility Functions
// =============================================================================

/**
 * @brief Get current system time in milliseconds
 * @return Current time as milliseconds since epoch
 */
UInt64 GetCurrentTimeMilliseconds();

/**
 * @brief Get high-resolution tick count
 * @return High-resolution tick count
 */
UInt64 GetHighResolutionTicks();

/**
 * @brief Convert milliseconds to high-resolution ticks
 * @param milliseconds Time in milliseconds
 * @return Equivalent high-resolution ticks
 */
UInt64 MillisecondsToTicks(UInt32 milliseconds);

/**
 * @brief Convert high-resolution ticks to milliseconds
 * @param ticks High-resolution ticks
 * @return Equivalent time in milliseconds
 */
UInt32 TicksToMilliseconds(UInt64 ticks);

#endif // TIMER_H
