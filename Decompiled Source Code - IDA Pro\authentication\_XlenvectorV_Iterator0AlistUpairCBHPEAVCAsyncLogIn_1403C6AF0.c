/*
 * Function: ?_<PERSON>len@?$vector@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@V?$allocator@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@3@@std@@KAXXZ
 * Address: 0x1403C6AF0
 */

void __fastcall __noreturn std::vector<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>,std::allocator<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>>>::_Xlen(std::vector<std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0>,std::allocator<std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> > > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-B8h]@1
  std::length_error v4; // [sp+20h] [bp-98h]@4
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > _Message; // [sp+68h] [bp-50h]@4
  unsigned __int8 v6; // [sp+98h] [bp-20h]@4
  __int64 v7; // [sp+A0h] [bp-18h]@4

  v1 = &v3;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = -2i64;
  memset(&v6, 0, sizeof(v6));
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
    &_Message,
    "vector<T> too long",
    v6);
  std::length_error::length_error(&v4, &_Message);
  CxxThrowException_0(&v4, &TI3_AVlength_error_std__);
}
