/**
 * @file monster_ai.h
 * @brief Monster AI system for RF Online Server
 * @details Refactored from CMonsterAI - provides intelligent monster behavior
 * 
 * Original IDA Pro functions:
 * - CMonsterAI class and related AI functions
 * 
 * <AUTHOR> Online Server Refactoring Project
 * @date 2025
 */

#ifndef MONSTER_AI_H
#define MONSTER_AI_H

#include "../../include/rf_types.h"
#include "../../include/rf_constants.h"
#include "../../include/rf_structures.h"
#include <memory>
#include <vector>

// =============================================================================
// Forward Declarations
// =============================================================================

class Monster;
class Character;
class PathManager;

// =============================================================================
// AI State Machine
// =============================================================================

/**
 * @brief AI state machine states
 * @details Replaces the original FSM states from CMonsterAI
 */
enum class AIStateMachine : UInt8 {
    IDLE = 0,
    PATROL,
    SEARCH,
    CHASE,
    ATTACK,
    FLEE,
    RETURN_HOME,
    ASSIST,
    DEAD,
    COUNT
};

/**
 * @brief AI behavior flags
 */
enum class AIBehaviorFlags : UInt32 {
    NONE = 0,
    SEARCH_ATTACK_PLAYER = 0x01,
    SEARCH_HELP_MONSTER = 0x02,
    EMOTION_ENABLED = 0x04,
    CONDITION_CHECK = 0x08,
    ASSIST_ENABLED = 0x10,
    ACTION_ENABLED = 0x20,
    MOVER_ENABLED = 0x40
};

// =============================================================================
// AI Timer Structure
// =============================================================================

/**
 * @brief AI timer for various timed behaviors
 * @details Replaces SF_Timer from original code
 */
struct AITimer {
    GameTime start_time;        ///< When timer was started
    GameTime duration;          ///< Timer duration
    bool is_active;             ///< Whether timer is running
    bool is_repeating;          ///< Whether timer repeats
    
    AITimer() : start_time(0), duration(0), is_active(false), is_repeating(false) {}
    
    void Start(GameTime duration_ms, bool repeating = false);
    void Stop();
    bool IsExpired(GameTime current_time) const;
    GameTime GetRemainingTime(GameTime current_time) const;
};

// =============================================================================
// MonsterAI Class
// =============================================================================

/**
 * @brief Monster AI controller
 * @details Manages monster behavior, decision making, and state transitions
 * 
 * This class replaces the original CMonsterAI and provides:
 * - State machine-based behavior
 * - Pathfinding integration
 * - Target selection and tracking
 * - Combat decision making
 * - Social behavior (assist, flee, etc.)
 */
class MonsterAI {
public:
    // =========================================================================
    // Constructor and Destructor
    // =========================================================================
    
    /**
     * @brief Constructor
     * @param owner Monster that owns this AI
     */
    explicit MonsterAI(Monster* owner);
    
    /**
     * @brief Destructor
     */
    ~MonsterAI();
    
    // Disable copy constructor and assignment operator
    MonsterAI(const MonsterAI&) = delete;
    MonsterAI& operator=(const MonsterAI&) = delete;
    
    // =========================================================================
    // Initialization and Configuration
    // =========================================================================
    
    /**
     * @brief Initialize the AI system
     * @param ai_type Type of AI behavior to use
     * @return true if initialization successful, false otherwise
     */
    bool Initialize(UInt32 ai_type);
    
    /**
     * @brief Shutdown the AI system
     */
    void Shutdown();
    
    /**
     * @brief Check if AI is initialized
     * @return true if initialized, false otherwise
     */
    bool IsInitialized() const { return is_initialized_; }
    
    // =========================================================================
    // State Management
    // =========================================================================
    
    /**
     * @brief Get current AI state
     * @return Current state
     */
    AIStateMachine GetCurrentState() const { return current_state_; }
    
    /**
     * @brief Get previous AI state
     * @return Previous state
     */
    AIStateMachine GetPreviousState() const { return previous_state_; }
    
    /**
     * @brief Set AI state
     * @param new_state New state to transition to
     */
    void SetState(AIStateMachine new_state);
    
    /**
     * @brief Get time in current state
     * @return Time spent in current state (milliseconds)
     */
    GameTime GetTimeInCurrentState() const;
    
    // =========================================================================
    // Target Management
    // =========================================================================
    
    /**
     * @brief Get current target
     * @return Pointer to target character (nullptr if no target)
     */
    Character* GetCurrentTarget() const { return current_target_; }
    
    /**
     * @brief Set current target
     * @param target New target character
     */
    void SetCurrentTarget(Character* target);
    
    /**
     * @brief Clear current target
     */
    void ClearTarget();
    
    /**
     * @brief Check if target is valid and reachable
     * @return true if target is valid, false otherwise
     */
    bool IsTargetValid() const;
    
    /**
     * @brief Find nearest enemy target
     * @return Pointer to nearest enemy (nullptr if none found)
     */
    Character* FindNearestEnemy();
    
    // =========================================================================
    // Behavior Control
    // =========================================================================
    
    /**
     * @brief Update AI logic
     * @param delta_time Time elapsed since last update (milliseconds)
     */
    void Update(UInt32 delta_time);
    
    /**
     * @brief Set behavior flags
     * @param flags Behavior flags to set
     */
    void SetBehaviorFlags(UInt32 flags) { behavior_flags_ = flags; }
    
    /**
     * @brief Get behavior flags
     * @return Current behavior flags
     */
    UInt32 GetBehaviorFlags() const { return behavior_flags_; }
    
    /**
     * @brief Check if specific behavior is enabled
     * @param flag Behavior flag to check
     * @return true if enabled, false otherwise
     */
    bool IsBehaviorEnabled(AIBehaviorFlags flag) const;
    
    // =========================================================================
    // Combat AI
    // =========================================================================
    
    /**
     * @brief Check if monster should attack
     * @return true if should attack, false otherwise
     */
    bool ShouldAttack() const;
    
    /**
     * @brief Check if monster should flee
     * @return true if should flee, false otherwise
     */
    bool ShouldFlee() const;
    
    /**
     * @brief Check if monster should call for help
     * @return true if should call for help, false otherwise
     */
    bool ShouldCallForHelp() const;
    
    /**
     * @brief Get attack range
     * @return Preferred attack range
     */
    float GetAttackRange() const;
    
    // =========================================================================
    // Movement and Pathfinding
    // =========================================================================
    
    /**
     * @brief Get pathfinding manager
     * @return Reference to path manager
     */
    PathManager& GetPathManager() { return *path_manager_; }
    const PathManager& GetPathManager() const { return *path_manager_; }
    
    /**
     * @brief Move towards target position
     * @param target_position Position to move towards
     * @return true if movement started, false otherwise
     */
    bool MoveTowards(const Position3D& target_position);
    
    /**
     * @brief Stop current movement
     */
    void StopMovement();
    
    /**
     * @brief Check if currently moving
     * @return true if moving, false otherwise
     */
    bool IsMoving() const { return is_moving_; }
    
    // =========================================================================
    // Social Behavior
    // =========================================================================
    
    /**
     * @brief Get assist monster
     * @return Pointer to monster being assisted (nullptr if none)
     */
    Monster* GetAssistMonster() const { return assist_monster_; }
    
    /**
     * @brief Set assist monster
     * @param monster Monster to assist
     */
    void SetAssistMonster(Monster* monster) { assist_monster_ = monster; }
    
    /**
     * @brief Call for help from nearby monsters
     */
    void CallForHelp();
    
    /**
     * @brief Respond to help call
     * @param caller Monster calling for help
     */
    void RespondToHelpCall(Monster* caller);

private:
    // =========================================================================
    // Private Methods
    // =========================================================================
    
    /**
     * @brief Update state machine
     * @param delta_time Time elapsed since last update
     */
    void UpdateStateMachine(UInt32 delta_time);
    
    /**
     * @brief Handle idle state
     * @param delta_time Time elapsed since last update
     */
    void HandleIdleState(UInt32 delta_time);
    
    /**
     * @brief Handle patrol state
     * @param delta_time Time elapsed since last update
     */
    void HandlePatrolState(UInt32 delta_time);
    
    /**
     * @brief Handle search state
     * @param delta_time Time elapsed since last update
     */
    void HandleSearchState(UInt32 delta_time);
    
    /**
     * @brief Handle chase state
     * @param delta_time Time elapsed since last update
     */
    void HandleChaseState(UInt32 delta_time);
    
    /**
     * @brief Handle attack state
     * @param delta_time Time elapsed since last update
     */
    void HandleAttackState(UInt32 delta_time);
    
    /**
     * @brief Handle flee state
     * @param delta_time Time elapsed since last update
     */
    void HandleFleeState(UInt32 delta_time);
    
    /**
     * @brief Handle return home state
     * @param delta_time Time elapsed since last update
     */
    void HandleReturnHomeState(UInt32 delta_time);
    
    /**
     * @brief Check for state transitions
     */
    void CheckStateTransitions();
    
    /**
     * @brief Update timers
     * @param delta_time Time elapsed since last update
     */
    void UpdateTimers(UInt32 delta_time);
    
    // =========================================================================
    // Member Variables
    // =========================================================================
    
    Monster* owner_;                        ///< Monster that owns this AI
    bool is_initialized_;                   ///< Initialization status
    
    // State machine
    AIStateMachine current_state_;          ///< Current AI state
    AIStateMachine previous_state_;         ///< Previous AI state
    GameTime state_change_time_;            ///< When state last changed
    
    // Target management
    Character* current_target_;             ///< Current target character
    GameTime last_target_check_time_;       ///< Last time targets were checked
    
    // Behavior configuration
    UInt32 behavior_flags_;                 ///< Behavior flags
    UInt32 ai_type_;                        ///< AI type identifier
    
    // Movement and pathfinding
    std::unique_ptr<PathManager> path_manager_;  ///< Pathfinding system
    bool is_moving_;                        ///< Whether currently moving
    Position3D movement_target_;            ///< Current movement target
    
    // Social behavior
    Monster* assist_monster_;               ///< Monster being assisted
    GameTime last_help_call_time_;          ///< Last time help was called
    
    // Timers
    AITimer skill_check_timers_[4];         ///< Skill check timers
    GameTime battle_mode_time_;             ///< Time in battle mode
    Int32 path_find_fail_count_;            ///< Consecutive pathfinding failures
    
    // Constants
    static constexpr GameTime TARGET_CHECK_INTERVAL = 1000;     ///< Target check interval (1 second)
    static constexpr GameTime HELP_CALL_COOLDOWN = 10000;      ///< Help call cooldown (10 seconds)
    static constexpr Int32 MAX_PATH_FIND_FAILURES = 3;         ///< Max pathfinding failures before giving up
};

#endif // MONSTER_AI_H
