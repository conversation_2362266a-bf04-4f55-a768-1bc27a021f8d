/*
 * Function: ?OnDisConnectSession@CHackShieldExSystem@@UEAAXH@Z
 * Address: 0x140417140
 */

void __fastcall CHackShieldExSystem::OnDisConnectSession(CHackShieldExSystem *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  BASE_HACKSHEILD_PARAM *v5; // [sp+20h] [bp-18h]@4
  CHackShieldExSystem *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = CHackShieldExSystem::GetParam(v6, n);
  if ( v5 )
    ((void (__fastcall *)(BASE_HACKSHEILD_PARAM *))v5->vfptr->OnDisConnect)(v5);
}
