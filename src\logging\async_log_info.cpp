/**
 * @file async_log_info.cpp
 * @brief Implementation of AsyncLogInfo class
 * @details Refactored from CAsyncLogInfo decompiled source
 * 
 * <AUTHOR> Online Server Refactoring Project
 * @date 2025
 */

#include "async_log_info.h"
#include "log_file.h"
#include "../system/timer.h"
#include <filesystem>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <ctime>

// =============================================================================
// Constructor and Destructor
// =============================================================================

AsyncLogInfo::AsyncLogInfo()
    : log_type_(AsyncLogType::NONE)
    , log_count_(0)
    , add_date_to_filename_(false)
    , is_initialized_(false)
    , update_timer_(nullptr)
{
    // Constructor body - all initialization done in initializer list
}

AsyncLogInfo::~AsyncLogInfo() {
    // Destructor - unique_ptr will automatically clean up timer
    // Atomic variables and strings clean up automatically
}

// =============================================================================
// Initialization and Configuration
// =============================================================================

bool AsyncLogInfo::Initialize(AsyncLogType log_type,
                              const char* directory_path,
                              const char* type_name,
                              bool add_date_to_filename,
                              UInt32 update_filename_delay_ms,
                              LogFile* loading_log) {
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    // Validate parameters (replaces original validation logic)
    if (static_cast<UInt32>(log_type) >= static_cast<UInt32>(AsyncLogType::MAX_LOG_TYPES) ||
        !directory_path || !type_name) {
        
        if (loading_log) {
            loading_log->WriteError("AsyncLogInfo::Initialize - Invalid parameters: "
                                   "type=%d, dir=%s, name=%s",
                                   static_cast<int>(log_type),
                                   directory_path ? directory_path : "NULL",
                                   type_name ? type_name : "NULL");
        }
        return false;
    }
    
    // Store configuration
    log_type_ = log_type;
    directory_path_ = directory_path;
    type_name_ = type_name;
    add_date_to_filename_ = add_date_to_filename;
    
    // Generate initial filename
    try {
        filename_ = GenerateFileName(type_name_, add_date_to_filename_);
        
        // Create full path
        std::filesystem::path full_path = std::filesystem::path(directory_path_) / filename_;
        filename_ = full_path.string();
        
        // Ensure directory exists
        std::filesystem::create_directories(directory_path_);
        
        // Remove existing file (replaces DeleteFileA from original)
        if (std::filesystem::exists(filename_)) {
            std::filesystem::remove(filename_);
        }
        
    } catch (const std::exception& e) {
        if (loading_log) {
            loading_log->WriteError("AsyncLogInfo::Initialize - Filesystem error: %s", e.what());
        }
        return false;
    }
    
    // Setup timer for filename updates if requested
    if (update_filename_delay_ms != 0) {
        if (update_filename_delay_ms < MIN_UPDATE_DELAY_MS) {
            if (loading_log) {
                loading_log->WriteError("AsyncLogInfo::Initialize - Update delay too small: %u ms "
                                       "(minimum: %u ms)", 
                                       update_filename_delay_ms, MIN_UPDATE_DELAY_MS);
            }
            return false;
        }
        
        try {
            update_timer_ = std::make_unique<Timer>();
            update_timer_->Start(update_filename_delay_ms, true); // Repeating timer
        } catch (const std::exception& e) {
            if (loading_log) {
                loading_log->WriteError("AsyncLogInfo::Initialize - Timer creation failed: %s", e.what());
            }
            return false;
        }
    }
    
    // Reset counters
    log_count_ = 0;
    is_initialized_ = true;
    
    if (loading_log) {
        loading_log->WriteInfo("AsyncLogInfo::Initialize - Successfully initialized: "
                              "type=%s, dir=%s, file=%s",
                              type_name_.c_str(), directory_path_.c_str(), filename_.c_str());
    }
    
    return true;
}

// =============================================================================
// Accessors
// =============================================================================

UInt32 AsyncLogInfo::GetLogCount() const {
    return log_count_.load();
}

const char* AsyncLogInfo::GetDirectoryPath() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return directory_path_.c_str();
}

const char* AsyncLogInfo::GetFileName() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return filename_.c_str();
}

const char* AsyncLogInfo::GetTypeName() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return type_name_.c_str();
}

// =============================================================================
// Operations
// =============================================================================

void AsyncLogInfo::IncrementLogCount() {
    log_count_.fetch_add(1);
}

void AsyncLogInfo::UpdateLogFileName() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!is_initialized_ || !add_date_to_filename_) {
        return;
    }
    
    try {
        // Generate new filename with current date/time
        std::string new_filename = GenerateFileName(type_name_, true);
        std::filesystem::path full_path = std::filesystem::path(directory_path_) / new_filename;
        std::string new_full_path = full_path.string();
        
        // Only update if filename actually changed
        if (new_full_path != filename_) {
            filename_ = new_full_path;
        }
        
    } catch (const std::exception&) {
        // Silently ignore filesystem errors during update
        // The old filename will continue to be used
    }
}

void AsyncLogInfo::ResetLogCount() {
    log_count_ = 0;
}

// =============================================================================
// Private Methods
// =============================================================================

std::string AsyncLogInfo::GenerateFileName(const std::string& base_name, bool include_date) const {
    std::ostringstream filename_stream;
    
    if (include_date) {
        std::string date_time = GetCurrentDateTimeString();
        filename_stream << base_name << "_" << date_time << ".log";
    } else {
        filename_stream << base_name << ".log";
    }
    
    return filename_stream.str();
}

std::string AsyncLogInfo::GetCurrentDateTimeString() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);
    
    std::ostringstream date_stream;
    date_stream << std::put_time(&tm, "%Y%m%d_%H%M%S");
    
    return date_stream.str();
}
