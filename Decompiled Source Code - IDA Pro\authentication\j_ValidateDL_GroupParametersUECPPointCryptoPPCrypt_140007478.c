/*
 * Function: j_?Validate@?$DL_GroupParameters@UECPPoint@CryptoPP@@@CryptoPP@@UEBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x140007478
 */

bool __fastcall CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::Validate(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *this, CryptoPP::RandomNumberGenerator *rng, unsigned int level)
{
  return CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::Validate(this, rng, level);
}
