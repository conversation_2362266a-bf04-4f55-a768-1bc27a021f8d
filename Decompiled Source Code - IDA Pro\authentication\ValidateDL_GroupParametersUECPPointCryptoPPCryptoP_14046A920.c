/*
 * Function: ?Validate@?$DL_GroupParameters@UECPPoint@CryptoPP@@@CryptoPP@@UEBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x14046A920
 */

char __fastcall CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::Validate(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *this, CryptoPP::RandomNumberGenerator *rng, unsigned int level)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int (__fastcall ***v5)(_QWORD); // rax@4
  char result; // al@5
  __int64 v7; // rax@9
  __int64 v8; // rax@9
  __int64 v9; // [sp+0h] [bp-68h]@1
  char v10; // [sp+20h] [bp-48h]@8
  __int64 v11; // [sp+28h] [bp-40h]@4
  int (__fastcall ***v12)(_QWORD); // [sp+30h] [bp-38h]@4
  __int64 v13; // [sp+38h] [bp-30h]@8
  __int64 v14; // [sp+40h] [bp-28h]@9
  __int64 v15; // [sp+48h] [bp-20h]@9
  __int64 v16; // [sp+50h] [bp-18h]@9
  int v17; // [sp+58h] [bp-10h]@10
  unsigned int v18; // [sp+5Ch] [bp-Ch]@13
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *v19; // [sp+70h] [bp+8h]@1
  CryptoPP::RandomNumberGenerator *v20; // [sp+78h] [bp+10h]@1
  unsigned int v21; // [sp+80h] [bp+18h]@1

  v21 = level;
  v20 = rng;
  v19 = this;
  v3 = &v9;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = *(_QWORD *)&v19[-1].gap8[0];
  LODWORD(v5) = (*(int (__fastcall **)(signed __int64))(v11 + 48))((signed __int64)v19[-1].gap8);
  v12 = v5;
  if ( (unsigned __int8)(**v5)(v5) )
  {
    if ( *(_DWORD *)&v19[-1].gap18[0] <= v21 )
    {
      v13 = *(_QWORD *)&v19[-1].gap8[0];
      v10 = (*(int (__fastcall **)(signed __int64, CryptoPP::RandomNumberGenerator *, _QWORD))(v13 + 128))(
              (signed __int64)v19[-1].gap8,
              v20,
              v21);
      v17 = v10
         && (v14 = *(_QWORD *)&v19[-1].gap8[0],
             LODWORD(v7) = (*(int (__fastcall **)(signed __int64))(v14 + 48))((signed __int64)v19[-1].gap8),
             v15 = v7,
             LODWORD(v8) = (*(int (__fastcall **)(signed __int64))(*(_QWORD *)&v19[-1].gap8[0] + 8i64))((signed __int64)v19[-1].gap8),
             v16 = *(_QWORD *)&v19[-1].gap8[0],
             (unsigned __int8)(*(int (__fastcall **)(signed __int64, _QWORD, __int64, __int64))(v16 + 136))(
                                (signed __int64)v19[-1].gap8,
                                v21,
                                v8,
                                v15));
      v10 = v17;
      if ( (_BYTE)v17 )
        v18 = v21 + 1;
      else
        v18 = 0;
      *(_DWORD *)&v19[-1].gap18[0] = v18;
      result = v10;
    }
    else
    {
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
