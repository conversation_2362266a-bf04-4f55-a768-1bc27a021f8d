/**
 * @file rf_structures.h
 * @brief Core game structures for RF Online Server
 * @details Defines the main data structures used throughout the game server
 * 
 * <AUTHOR> Online Server Refactoring Project
 * @date 2025
 */

#ifndef RF_STRUCTURES_H
#define RF_STRUCTURES_H

#include "rf_types.h"
#include "rf_constants.h"

// =============================================================================
// Forward Declarations
// =============================================================================

typedef struct Player Player;
typedef struct Monster Monster;
typedef struct MapData MapData;
typedef struct UserDatabase UserDatabase;
typedef struct Guild Guild;

// =============================================================================
// Basic Game Structures
// =============================================================================

/**
 * @brief 3D position with rotation
 */
typedef struct {
    Position3D position;
    float rotation_x;
    float rotation_y;
    float rotation_z;
} Transform;

/**
 * @brief Bounding box for collision detection
 */
typedef struct {
    Position3D min;
    Position3D max;
} BoundingBox;

/**
 * @brief Time-based information
 */
typedef struct {
    GameTime creation_time;
    GameTime last_update;
    GameTime expiration_time;
    bool is_expired;
} TimeInfo;

// =============================================================================
// Character and Player Structures
// =============================================================================

/**
 * @brief Basic character statistics
 */
typedef struct {
    UInt16 strength;
    UInt16 dexterity;
    UInt16 intelligence;
    UInt16 constitution;
    UInt16 leadership;
    UInt16 luck;
} CharacterStats;

/**
 * @brief Character vital information
 */
typedef struct {
    UInt32 current_hp;
    UInt32 max_hp;
    UInt32 current_fp;  // Force Points
    UInt32 max_fp;
    UInt32 current_sp;  // Stamina Points
    UInt32 max_sp;
} VitalStats;

/**
 * @brief Character appearance data
 */
typedef struct {
    UInt32 face_id;
    UInt32 hair_id;
    UInt32 skin_color;
    UInt32 hair_color;
    UInt8 gender;       // 0 = male, 1 = female
} CharacterAppearance;

/**
 * @brief Basic character information
 */
typedef struct {
    CharacterSerial serial;
    char name[MAX_CHARACTER_NAME_LENGTH];
    CharacterRace race;
    CharacterClass class_type;
    Level level;
    Experience experience;
    Gold gold;
    CharacterStats stats;
    VitalStats vitals;
    CharacterAppearance appearance;
    Transform transform;
    PlayerState state;
    MapIndex current_map;
    TimeInfo time_info;
} CharacterData;

// =============================================================================
// Item and Inventory Structures
// =============================================================================

/**
 * @brief Item instance data
 */
typedef struct {
    UInt32 item_id;
    UInt32 serial;
    ItemType type;
    UInt16 quantity;
    UInt8 enhancement_level;
    UInt32 durability;
    UInt32 max_durability;
    TimeInfo time_info;
    bool is_bound;
} ItemInstance;

/**
 * @brief Inventory slot
 */
typedef struct {
    ItemInstance item;
    bool is_occupied;
    UInt8 slot_index;
} InventorySlot;

/**
 * @brief Player inventory
 */
typedef struct {
    InventorySlot slots[MAX_INVENTORY_SLOTS];
    InventorySlot equipment[MAX_EQUIPMENT_SLOTS];
    UInt8 used_slots;
    Gold gold;
} PlayerInventory;

// =============================================================================
// Monster and AI Structures
// =============================================================================

/**
 * @brief Monster statistics
 */
typedef struct {
    UInt32 attack_power;
    UInt32 defense;
    UInt32 accuracy;
    UInt32 dodge;
    UInt32 critical_rate;
    float move_speed;
    float attack_speed;
    float aggro_range;
    float chase_range;
} MonsterStats;

/**
 * @brief Monster AI data
 */
typedef struct {
    AIState current_state;
    AIState previous_state;
    ObjectSerial target_serial;
    Position3D spawn_position;
    Position3D patrol_center;
    float patrol_radius;
    GameTime last_state_change;
    GameTime next_action_time;
    bool is_aggressive;
} MonsterAI;

/**
 * @brief Basic monster information
 */
typedef struct {
    ObjectSerial serial;
    UInt32 template_id;
    char name[MAX_MONSTER_NAME_LENGTH];
    MonsterGrade grade;
    Level level;
    VitalStats vitals;
    MonsterStats stats;
    MonsterAI ai;
    Transform transform;
    MonsterState state;
    MapIndex current_map;
    TimeInfo time_info;
    GameTime respawn_time;
} MonsterData;

// =============================================================================
// Guild Structures
// =============================================================================

/**
 * @brief Guild member information
 */
typedef struct {
    CharacterSerial character_serial;
    char character_name[MAX_CHARACTER_NAME_LENGTH];
    GuildRank rank;
    Level level;
    CharacterClass class_type;
    GameTime join_date;
    GameTime last_login;
    bool is_online;
} GuildMember;

/**
 * @brief Guild information
 */
typedef struct {
    GuildSerial serial;
    char name[MAX_GUILD_NAME_LENGTH];
    char description[256];
    CharacterSerial leader_serial;
    Level guild_level;
    UInt32 guild_points;
    Gold guild_gold;
    GuildMember members[MAX_GUILD_MEMBERS];
    UInt8 member_count;
    TimeInfo time_info;
} GuildData;

// =============================================================================
// Map and World Structures
// =============================================================================

/**
 * @brief Portal information for map transitions
 */
typedef struct {
    UInt32 portal_id;
    Position3D position;
    MapIndex destination_map;
    Position3D destination_position;
    Level required_level;
    bool is_active;
} PortalData;

/**
 * @brief Map sector for spatial partitioning
 */
typedef struct {
    UInt16 sector_x;
    UInt16 sector_y;
    BoundingBox bounds;
    ObjectSerial* objects;
    UInt32 object_count;
    UInt32 max_objects;
} MapSector;

/**
 * @brief Map information
 */
typedef struct {
    MapIndex index;
    char name[MAX_MAP_NAME_LENGTH];
    MapType type;
    UInt32 width;
    UInt32 height;
    Level min_level;
    Level max_level;
    bool pvp_enabled;
    PortalData* portals;
    UInt32 portal_count;
    MapSector* sectors;
    UInt32 sector_count;
    TimeInfo time_info;
} MapInfo;

// =============================================================================
// Network and Session Structures
// =============================================================================

/**
 * @brief Network session information
 */
typedef struct {
    SessionID session_id;
    ConnectionID connection_id;
    char ip_address[MAX_IP_ADDRESS_LENGTH];
    UInt16 port;
    AuthenticationState auth_state;
    AccountSerial account_serial;
    CharacterSerial character_serial;
    GameTime connect_time;
    GameTime last_activity;
    bool is_active;
} NetworkSession;

/**
 * @brief Message header for network communication
 */
typedef struct {
    MessageType type;
    PacketSize size;
    SessionID session_id;
    UInt32 sequence_number;
    UInt32 checksum;
} MessageHeader;

// =============================================================================
// Database Structures
// =============================================================================

/**
 * @brief Database query result
 */
typedef struct {
    ResultCode result_code;
    UInt32 affected_rows;
    void* data;
    size_t data_size;
    char error_message[256];
} DatabaseResult;

/**
 * @brief Database connection info
 */
typedef struct {
    char connection_string[MAX_DB_CONNECTION_STRING];
    char username[64];
    char password[64];
    bool is_connected;
    GameTime last_ping;
    UInt32 query_count;
} DatabaseConnection;

#endif // RF_STRUCTURES_H
