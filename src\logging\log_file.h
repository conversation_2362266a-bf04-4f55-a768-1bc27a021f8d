/**
 * @file log_file.h
 * @brief File-based logging system for RF Online Server
 * @details Refactored from CLogFile - provides thread-safe file logging
 * 
 * <AUTHOR> Online Server Refactoring Project
 * @date 2025
 */

#ifndef LOG_FILE_H
#define LOG_FILE_H

#include "../../include/rf_types.h"
#include "../../include/rf_constants.h"
#include <fstream>
#include <string>
#include <mutex>
#include <memory>

// =============================================================================
// Enumerations
// =============================================================================

/**
 * @brief Log levels for filtering and categorization
 */
enum class LogLevel : UInt8 {
    TRACE = 0,      ///< Detailed trace information
    DEBUG = 1,      ///< Debug information
    INFO = 2,       ///< General information
    WARNING = 3,    ///< Warning messages
    ERROR = 4,      ///< Error messages
    CRITICAL = 5    ///< Critical error messages
};

/**
 * @brief Log output modes
 */
enum class LogOutputMode : UInt8 {
    FILE_ONLY = 0,      ///< Write to file only
    CONSOLE_ONLY = 1,   ///< Write to console only
    BOTH = 2            ///< Write to both file and console
};

// =============================================================================
// LogFile Class
// =============================================================================

/**
 * @brief Thread-safe file logging class
 * @details Provides comprehensive logging functionality including:
 * - Multiple log levels
 * - Thread-safe operations
 * - Automatic file rotation
 * - Console output support
 * - Formatted message support
 */
class LogFile {
public:
    // =========================================================================
    // Constructor and Destructor
    // =========================================================================
    
    /**
     * @brief Default constructor
     */
    LogFile();
    
    /**
     * @brief Constructor with filename
     * @param filename Path to log file
     */
    explicit LogFile(const std::string& filename);
    
    /**
     * @brief Destructor
     * @details Ensures file is properly closed
     */
    ~LogFile();
    
    // Disable copy constructor and assignment operator
    LogFile(const LogFile&) = delete;
    LogFile& operator=(const LogFile&) = delete;
    
    // =========================================================================
    // File Operations
    // =========================================================================
    
    /**
     * @brief Open log file for writing
     * @param filename Path to log file
     * @param append Whether to append to existing file (true) or overwrite (false)
     * @return true if file opened successfully, false otherwise
     */
    bool Open(const std::string& filename, bool append = true);
    
    /**
     * @brief Close the log file
     */
    void Close();
    
    /**
     * @brief Check if log file is open
     * @return true if file is open, false otherwise
     */
    bool IsOpen() const;
    
    /**
     * @brief Flush pending writes to disk
     */
    void Flush();
    
    // =========================================================================
    // Configuration
    // =========================================================================
    
    /**
     * @brief Set minimum log level
     * @param level Minimum level to log (messages below this level are ignored)
     */
    void SetLogLevel(LogLevel level);
    
    /**
     * @brief Get current log level
     * @return Current minimum log level
     */
    LogLevel GetLogLevel() const;
    
    /**
     * @brief Set output mode
     * @param mode Where to write log messages
     */
    void SetOutputMode(LogOutputMode mode);
    
    /**
     * @brief Get current output mode
     * @return Current output mode
     */
    LogOutputMode GetOutputMode() const;
    
    /**
     * @brief Enable or disable timestamps
     * @param enable Whether to include timestamps in log messages
     */
    void SetTimestampEnabled(bool enable);
    
    /**
     * @brief Check if timestamps are enabled
     * @return true if timestamps are enabled, false otherwise
     */
    bool IsTimestampEnabled() const;
    
    // =========================================================================
    // Logging Methods
    // =========================================================================
    
    /**
     * @brief Write a log message with specified level
     * @param level Log level
     * @param message Message to log
     */
    void Write(LogLevel level, const std::string& message);
    
    /**
     * @brief Write a formatted log message with specified level
     * @param level Log level
     * @param format Printf-style format string
     * @param ... Format arguments
     */
    void Write(LogLevel level, const char* format, ...);
    
    /**
     * @brief Write a trace message
     * @param message Message to log
     */
    void WriteTrace(const std::string& message);
    
    /**
     * @brief Write a formatted trace message
     * @param format Printf-style format string
     * @param ... Format arguments
     */
    void WriteTrace(const char* format, ...);
    
    /**
     * @brief Write a debug message
     * @param message Message to log
     */
    void WriteDebug(const std::string& message);
    
    /**
     * @brief Write a formatted debug message
     * @param format Printf-style format string
     * @param ... Format arguments
     */
    void WriteDebug(const char* format, ...);
    
    /**
     * @brief Write an info message
     * @param message Message to log
     */
    void WriteInfo(const std::string& message);
    
    /**
     * @brief Write a formatted info message
     * @param format Printf-style format string
     * @param ... Format arguments
     */
    void WriteInfo(const char* format, ...);
    
    /**
     * @brief Write a warning message
     * @param message Message to log
     */
    void WriteWarning(const std::string& message);
    
    /**
     * @brief Write a formatted warning message
     * @param format Printf-style format string
     * @param ... Format arguments
     */
    void WriteWarning(const char* format, ...);
    
    /**
     * @brief Write an error message
     * @param message Message to log
     */
    void WriteError(const std::string& message);
    
    /**
     * @brief Write a formatted error message
     * @param format Printf-style format string
     * @param ... Format arguments
     */
    void WriteError(const char* format, ...);
    
    /**
     * @brief Write a critical message
     * @param message Message to log
     */
    void WriteCritical(const std::string& message);
    
    /**
     * @brief Write a formatted critical message
     * @param format Printf-style format string
     * @param ... Format arguments
     */
    void WriteCritical(const char* format, ...);

private:
    // =========================================================================
    // Private Methods
    // =========================================================================
    
    /**
     * @brief Write message to output destinations
     * @param level Log level
     * @param message Formatted message
     */
    void WriteToOutput(LogLevel level, const std::string& message);
    
    /**
     * @brief Get string representation of log level
     * @param level Log level
     * @return String representation
     */
    std::string GetLogLevelString(LogLevel level) const;
    
    /**
     * @brief Get current timestamp string
     * @return Formatted timestamp
     */
    std::string GetTimestamp() const;
    
    /**
     * @brief Format message with timestamp and level
     * @param level Log level
     * @param message Raw message
     * @return Formatted message
     */
    std::string FormatMessage(LogLevel level, const std::string& message) const;
    
    // =========================================================================
    // Member Variables
    // =========================================================================
    
    std::unique_ptr<std::ofstream> file_stream_;    ///< Output file stream
    std::string filename_;                          ///< Current log filename
    LogLevel min_log_level_;                        ///< Minimum log level
    LogOutputMode output_mode_;                     ///< Output mode
    bool timestamp_enabled_;                        ///< Whether to include timestamps
    mutable std::mutex mutex_;                      ///< Mutex for thread safety
    
    // Constants
    static constexpr size_t MAX_MESSAGE_LENGTH = 4096;  ///< Maximum message length
};

#endif // LOG_FILE_H
