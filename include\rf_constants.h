/**
 * @file rf_constants.h
 * @brief Game constants and configuration values for RF Online Server
 * @details Contains all game-specific constants, limits, and configuration values
 * 
 * <AUTHOR> Online Server Refactoring Project
 * @date 2025
 */

#ifndef RF_CONSTANTS_H
#define RF_CONSTANTS_H

#include "rf_types.h"

// =============================================================================
// Server Configuration Constants
// =============================================================================

#define SERVER_VERSION_MAJOR    1
#define SERVER_VERSION_MINOR    0
#define SERVER_VERSION_PATCH    0

#define MAX_SERVER_NAME_LENGTH  64
#define MAX_DATABASE_NAME_LENGTH 128
#define MAX_IP_ADDRESS_LENGTH   16

// =============================================================================
// Player and Character Constants
// =============================================================================

#define MAX_CHARACTER_NAME_LENGTH   32
#define MAX_ACCOUNT_NAME_LENGTH     32
#define MAX_PASSWORD_LENGTH         32
#define MAX_CHARACTERS_PER_ACCOUNT  4
#define MAX_LEVEL                   99
#define MAX_STAT_VALUE              999

// Character classes
typedef enum {
    CLASS_WARRIOR = 0,
    CLASS_RANGER,
    CLASS_SPIRITUALIST,
    CLASS_SPECIALIST,
    CLASS_COUNT
} CharacterClass;

// Character races
typedef enum {
    RACE_BELLATO = 0,
    RACE_CORA,
    RACE_ACCRETIA,
    RACE_COUNT
} CharacterRace;

// =============================================================================
// Game World Constants
// =============================================================================

#define MAX_MAP_NAME_LENGTH     64
#define MAX_ZONE_NAME_LENGTH    64
#define MAX_MAPS_PER_ZONE       16
#define WORLD_COORDINATE_SCALE  100.0f

// Map types
typedef enum {
    MAP_TYPE_NORMAL = 0,
    MAP_TYPE_DUNGEON,
    MAP_TYPE_PVP,
    MAP_TYPE_GUILD_BATTLE,
    MAP_TYPE_SPECIAL
} MapType;

// =============================================================================
// Monster and AI Constants
// =============================================================================

#define MAX_MONSTER_NAME_LENGTH     32
#define MAX_MONSTERS_PER_MAP        1000
#define MAX_AGGRO_RANGE             50.0f
#define DEFAULT_RESPAWN_TIME        300000  // 5 minutes in milliseconds

// Monster grades
typedef enum {
    MONSTER_GRADE_NORMAL = 0,
    MONSTER_GRADE_ELITE,
    MONSTER_GRADE_BOSS,
    MONSTER_GRADE_RAID_BOSS,
    MONSTER_GRADE_COUNT
} MonsterGrade;

// Monster AI states
typedef enum {
    AI_STATE_IDLE = 0,
    AI_STATE_PATROL,
    AI_STATE_SEARCH,
    AI_STATE_CHASE,
    AI_STATE_ATTACK,
    AI_STATE_RETURN,
    AI_STATE_DEAD
} AIState;

// =============================================================================
// Item and Inventory Constants
// =============================================================================

#define MAX_ITEM_NAME_LENGTH        64
#define MAX_INVENTORY_SLOTS         40
#define MAX_EQUIPMENT_SLOTS         12
#define MAX_STORAGE_SLOTS           100
#define MAX_ITEM_STACK_SIZE         999

// Item types
typedef enum {
    ITEM_TYPE_WEAPON = 0,
    ITEM_TYPE_ARMOR,
    ITEM_TYPE_ACCESSORY,
    ITEM_TYPE_CONSUMABLE,
    ITEM_TYPE_MATERIAL,
    ITEM_TYPE_QUEST,
    ITEM_TYPE_SPECIAL,
    ITEM_TYPE_COUNT
} ItemType;

// Equipment slots
typedef enum {
    EQUIP_SLOT_HELMET = 0,
    EQUIP_SLOT_UPPER,
    EQUIP_SLOT_LOWER,
    EQUIP_SLOT_WEAPON,
    EQUIP_SLOT_SHOES,
    EQUIP_SLOT_CLOAK,
    EQUIP_SLOT_SHIELD,
    EQUIP_SLOT_GLOVES,
    EQUIP_SLOT_COUNT
} EquipmentSlot;

// =============================================================================
// Guild and Social Constants
// =============================================================================

#define MAX_GUILD_NAME_LENGTH       32
#define MAX_GUILD_MEMBERS           100
#define MAX_GUILD_LEVEL             20
#define MAX_PARTY_MEMBERS           8

// Guild ranks
typedef enum {
    GUILD_RANK_MEMBER = 0,
    GUILD_RANK_OFFICER,
    GUILD_RANK_VICE_LEADER,
    GUILD_RANK_LEADER,
    GUILD_RANK_COUNT
} GuildRank;

// =============================================================================
// Combat and Skills Constants
// =============================================================================

#define MAX_SKILL_LEVEL             50
#define MAX_FORCE_LEVEL             50
#define MAX_DAMAGE_VALUE            99999
#define CRITICAL_HIT_MULTIPLIER     1.5f

// Damage types
typedef enum {
    DAMAGE_TYPE_PHYSICAL = 0,
    DAMAGE_TYPE_FORCE,
    DAMAGE_TYPE_FIRE,
    DAMAGE_TYPE_WATER,
    DAMAGE_TYPE_EARTH,
    DAMAGE_TYPE_WIND,
    DAMAGE_TYPE_COUNT
} DamageType;

// =============================================================================
// Network and Protocol Constants
// =============================================================================

#define MAX_PACKET_SIZE             8192
#define MAX_MESSAGE_LENGTH          512
#define NETWORK_TIMEOUT_MS          30000
#define HEARTBEAT_INTERVAL_MS       10000

// Message types (simplified from original complex system)
typedef enum {
    MSG_TYPE_LOGIN = 0x1000,
    MSG_TYPE_LOGOUT,
    MSG_TYPE_CHAR_SELECT,
    MSG_TYPE_ENTER_WORLD,
    MSG_TYPE_MOVE,
    MSG_TYPE_ATTACK,
    MSG_TYPE_CHAT,
    MSG_TYPE_TRADE,
    MSG_TYPE_GUILD,
    MSG_TYPE_PARTY,
    MSG_TYPE_SYSTEM = 0x9000
} MessageType;

// =============================================================================
// Database Constants
// =============================================================================

#define MAX_SQL_QUERY_LENGTH        2048
#define MAX_DB_CONNECTION_STRING    256
#define DB_TIMEOUT_SECONDS          30
#define MAX_CONCURRENT_QUERIES      100

// =============================================================================
// Economy Constants
// =============================================================================

#define MAX_GOLD_AMOUNT             2000000000U
#define TRADE_TAX_RATE              0.05f      // 5% tax
#define STORAGE_COST_PER_SLOT       1000

// =============================================================================
// Time and Scheduling Constants
// =============================================================================

#define GAME_TICK_RATE              20         // 20 FPS
#define GAME_TICK_INTERVAL_MS       50         // 1000/20
#define SAVE_INTERVAL_MS            300000     // 5 minutes
#define CLEANUP_INTERVAL_MS         600000     // 10 minutes

// =============================================================================
// Security and Anti-Cheat Constants
// =============================================================================

#define MAX_LOGIN_ATTEMPTS          3
#define LOGIN_LOCKOUT_TIME_MS       300000     // 5 minutes
#define MAX_PACKET_RATE             100        // packets per second
#define SPEED_HACK_THRESHOLD        1.5f       // 150% of normal speed

// =============================================================================
// File and Resource Constants
// =============================================================================

#define MAX_FILENAME_LENGTH         256
#define MAX_PATH_LENGTH             512
#define CONFIG_FILE_EXTENSION       ".cfg"
#define LOG_FILE_EXTENSION          ".log"
#define DATA_FILE_EXTENSION         ".dat"

#endif // RF_CONSTANTS_H
