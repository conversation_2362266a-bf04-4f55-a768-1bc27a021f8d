/*
 * Function: j_?Init@CAsyncLogInfo@@QEAA_NW4ASYNC_LOG_TYPE@@PEBD1_NKAEAVCLogFile@@@Z
 * Address: 0x14000C3F1
 */

bool __fastcall CAsyncLogInfo::Init(CAsyncLogInfo *this, ASYNC_LOG_TYPE eType, const char *szDirPath, const char *szTypeName, bool bAddDateFileName, unsigned int dwUpdateFileNameDelay, CLogFile *logLoading)
{
  return CAsyncLogInfo::Init(this, eType, szDirPath, szTypeName, bAddDateFileName, dwUpdateFileNameDelay, logLoading);
}
