/**
 * @file rf_types.h
 * @brief Modern type definitions for RF Online Server
 * @details Replaces IDA Pro generic types with meaningful, standard C types
 * 
 * This file provides clean, modern type definitions to replace the generic
 * IDA Pro decompiled types like __int8, __int16, _DWORD, _BYTE etc.
 * 
 * <AUTHOR> Online Server Refactoring Project
 * @date 2025
 */

#ifndef RF_TYPES_H
#define RF_TYPES_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

// =============================================================================
// Basic Integer Types (replacing IDA Pro types)
// =============================================================================

// Replace __int8, __int16, __int32, __int64
typedef int8_t      Int8;
typedef int16_t     Int16;
typedef int32_t     Int32;
typedef int64_t     Int64;

typedef uint8_t     UInt8;
typedef uint16_t    UInt16;
typedef uint32_t    UInt32;
typedef uint64_t    UInt64;

// Replace _BYTE, _WORD, _DWORD, _QWORD
typedef uint8_t     Byte;
typedef uint16_t    Word;
typedef uint32_t    DWord;
typedef uint64_t    QWord;

// =============================================================================
// Game-Specific Types
// =============================================================================

// Character and Object IDs
typedef UInt32      CharacterSerial;
typedef UInt32      ObjectSerial;
typedef UInt32      AccountSerial;
typedef UInt32      GuildSerial;
typedef UInt16      MapIndex;
typedef UInt16      ZoneIndex;

// Game coordinates and positioning
typedef float       GameCoordinate;
typedef struct {
    GameCoordinate x, y, z;
} Position3D;

typedef struct {
    GameCoordinate x, y;
} Position2D;

// Game time and timing
typedef UInt32      GameTime;
typedef UInt32      TickCount;

// Resource and economy types
typedef UInt32      Gold;
typedef UInt32      Experience;
typedef UInt16      Level;
typedef UInt8       Race;
typedef UInt8       Class;

// =============================================================================
// Network and Communication Types
// =============================================================================

typedef UInt16      MessageType;
typedef UInt32      SessionID;
typedef UInt16      PacketSize;

// Connection identifier structure
typedef struct {
    UInt16 index;
    UInt32 serial;
} ConnectionID;

// =============================================================================
// Database Types
// =============================================================================

typedef UInt32      DatabaseID;
typedef char        DatabaseString[256];
typedef char        ShortString[64];
typedef char        LongString[512];

// =============================================================================
// Game State Enumerations
// =============================================================================

// Player states
typedef enum {
    PLAYER_STATE_OFFLINE = 0,
    PLAYER_STATE_LOBBY,
    PLAYER_STATE_INGAME,
    PLAYER_STATE_TRADING,
    PLAYER_STATE_COMBAT,
    PLAYER_STATE_DEAD
} PlayerState;

// Monster states  
typedef enum {
    MONSTER_STATE_IDLE = 0,
    MONSTER_STATE_PATROL,
    MONSTER_STATE_COMBAT,
    MONSTER_STATE_FLEEING,
    MONSTER_STATE_DEAD,
    MONSTER_STATE_RESPAWNING
} MonsterState;

// Authentication states
typedef enum {
    AUTH_STATE_NONE = 0,
    AUTH_STATE_CONNECTING,
    AUTH_STATE_AUTHENTICATING,
    AUTH_STATE_AUTHENTICATED,
    AUTH_STATE_FAILED
} AuthenticationState;

// =============================================================================
// Error Codes and Result Types
// =============================================================================

typedef enum {
    RESULT_SUCCESS = 0,
    RESULT_FAILURE = 1,
    RESULT_INVALID_PARAMETER = 2,
    RESULT_INSUFFICIENT_RESOURCES = 3,
    RESULT_ACCESS_DENIED = 4,
    RESULT_NOT_FOUND = 5,
    RESULT_TIMEOUT = 6,
    RESULT_NETWORK_ERROR = 7,
    RESULT_DATABASE_ERROR = 8
} ResultCode;

// =============================================================================
// Callback and Function Pointer Types
// =============================================================================

typedef void (*EventCallback)(void* userData);
typedef bool (*ValidationFunction)(const void* data);
typedef ResultCode (*ProcessFunction)(void* context, void* data);

// =============================================================================
// Memory and Buffer Types
// =============================================================================

typedef struct {
    void*   data;
    size_t  size;
    size_t  capacity;
} Buffer;

typedef struct {
    char*   string;
    size_t  length;
    size_t  capacity;
} DynamicString;

// =============================================================================
// Utility Macros
// =============================================================================

#define INVALID_ID          0xFFFFFFFF
#define INVALID_INDEX       0xFFFF
#define MAX_PLAYERS         10000
#define MAX_MONSTERS        50000
#define MAX_MAPS            1000

// Safe casting macros
#define SAFE_CAST(type, value) ((type)(value))
#define IS_VALID_ID(id) ((id) != INVALID_ID)
#define IS_VALID_INDEX(idx) ((idx) != INVALID_INDEX)

// Array size calculation
#define ARRAY_SIZE(arr) (sizeof(arr) / sizeof((arr)[0]))

// Bit manipulation helpers
#define SET_BIT(value, bit)     ((value) |= (1U << (bit)))
#define CLEAR_BIT(value, bit)   ((value) &= ~(1U << (bit)))
#define TEST_BIT(value, bit)    (((value) & (1U << (bit))) != 0)

#endif // RF_TYPES_H
