/**
 * @file character.h
 * @brief Base character class for RF Online Server
 * @details Refactored from CCharacter - provides common functionality for players and monsters
 * 
 * Original IDA Pro functions:
 * - CCharacter::Create
 * - CCharacter::GetPosition
 * - CCharacter::SetPosition
 * - CCharacter::GetCurrentMap
 * - CCharacter::SetCurrentMap
 * 
 * <AUTHOR> Online Server Refactoring Project
 * @date 2025
 */

#ifndef CHARACTER_H
#define CHARACTER_H

#include "../../include/rf_types.h"
#include "../../include/rf_constants.h"
#include "../../include/rf_structures.h"
#include <atomic>
#include <mutex>

// =============================================================================
// Forward Declarations
// =============================================================================

class MapData;
class NetworkSession;

// =============================================================================
// Character Creation Data
// =============================================================================

/**
 * @brief Base data structure for character creation
 * @details Replaces _character_create_setdata from original code
 */
struct CharacterCreateData {
    CharacterSerial serial;                 ///< Character serial number
    char name[MAX_CHARACTER_NAME_LENGTH];   ///< Character name
    CharacterRace race;                     ///< Character race
    CharacterClass class_type;              ///< Character class
    Level level;                            ///< Character level
    Position3D position;                    ///< Initial position
    MapIndex map_index;                     ///< Starting map
    
    CharacterCreateData()
        : serial(INVALID_ID)
        , race(RACE_BELLATO)
        , class_type(CLASS_WARRIOR)
        , level(1)
        , position{0.0f, 0.0f, 0.0f}
        , map_index(0)
    {
        name[0] = '\0';
    }
};

// =============================================================================
// Character Class
// =============================================================================

/**
 * @brief Base character class
 * @details Provides common functionality for all character entities (players, monsters, NPCs)
 * 
 * This class replaces the original CCharacter and provides:
 * - Position and movement management
 * - Map and zone tracking
 * - Basic character properties
 * - Thread-safe operations
 * - Virtual interface for derived classes
 */
class Character {
public:
    // =========================================================================
    // Constructor and Destructor
    // =========================================================================
    
    /**
     * @brief Default constructor
     */
    Character();
    
    /**
     * @brief Virtual destructor
     * @details Ensures proper cleanup in derived classes
     */
    virtual ~Character();
    
    // Disable copy constructor and assignment operator
    Character(const Character&) = delete;
    Character& operator=(const Character&) = delete;
    
    // =========================================================================
    // Creation and Initialization
    // =========================================================================
    
    /**
     * @brief Create and initialize character
     * @param create_data Character creation parameters
     * @return true if creation successful, false otherwise
     * 
     * Replaces: CCharacter::Create
     */
    virtual bool Create(const CharacterCreateData& create_data);
    
    /**
     * @brief Destroy the character
     * @details Virtual method for cleanup in derived classes
     */
    virtual void Destroy();
    
    /**
     * @brief Check if character is properly initialized
     * @return true if initialized, false otherwise
     */
    bool IsInitialized() const { return is_initialized_; }
    
    // =========================================================================
    // Basic Properties
    // =========================================================================
    
    /**
     * @brief Get character serial number
     * @return Unique character serial
     */
    CharacterSerial GetSerial() const { return character_serial_; }
    
    /**
     * @brief Get character name
     * @return Character name string
     */
    const char* GetName() const;
    
    /**
     * @brief Set character name
     * @param name New character name
     * @return true if name set successfully, false otherwise
     */
    bool SetName(const char* name);
    
    /**
     * @brief Get character race
     * @return Character race
     */
    CharacterRace GetRace() const { return race_; }
    
    /**
     * @brief Get character class
     * @return Character class
     */
    CharacterClass GetClass() const { return class_type_; }
    
    /**
     * @brief Get character level
     * @return Current level
     */
    Level GetLevel() const { return level_; }
    
    /**
     * @brief Set character level
     * @param level New level
     */
    void SetLevel(Level level) { level_ = level; }
    
    // =========================================================================
    // Position and Movement
    // =========================================================================
    
    /**
     * @brief Get current position
     * @return Current 3D position
     * 
     * Replaces: CCharacter::GetPosition
     */
    Position3D GetPosition() const;
    
    /**
     * @brief Set current position
     * @param position New position
     * 
     * Replaces: CCharacter::SetPosition
     */
    void SetPosition(const Position3D& position);
    
    /**
     * @brief Get current position (individual coordinates)
     * @param x Output X coordinate
     * @param y Output Y coordinate
     * @param z Output Z coordinate
     */
    void GetPosition(float& x, float& y, float& z) const;
    
    /**
     * @brief Set current position (individual coordinates)
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     */
    void SetPosition(float x, float y, float z);
    
    /**
     * @brief Calculate distance to another position
     * @param other_position Target position
     * @return Distance in world units
     */
    float GetDistanceTo(const Position3D& other_position) const;
    
    /**
     * @brief Calculate distance to another character
     * @param other_character Target character
     * @return Distance in world units
     */
    float GetDistanceTo(const Character* other_character) const;
    
    // =========================================================================
    // Map and Zone Management
    // =========================================================================
    
    /**
     * @brief Get current map index
     * @return Current map index
     * 
     * Replaces: CCharacter::GetCurrentMap
     */
    MapIndex GetCurrentMap() const { return current_map_; }
    
    /**
     * @brief Set current map
     * @param map_index New map index
     * 
     * Replaces: CCharacter::SetCurrentMap
     */
    void SetCurrentMap(MapIndex map_index);
    
    /**
     * @brief Get current zone index
     * @return Current zone index
     */
    ZoneIndex GetCurrentZone() const { return current_zone_; }
    
    /**
     * @brief Set current zone
     * @param zone_index New zone index
     */
    void SetCurrentZone(ZoneIndex zone_index) { current_zone_ = zone_index; }
    
    // =========================================================================
    // State and Status
    // =========================================================================
    
    /**
     * @brief Check if character is active
     * @return true if active, false otherwise
     */
    bool IsActive() const { return is_active_; }
    
    /**
     * @brief Set character active state
     * @param active New active state
     */
    void SetActive(bool active) { is_active_ = active; }
    
    /**
     * @brief Get last update time
     * @return Time of last update
     */
    GameTime GetLastUpdateTime() const { return last_update_time_; }
    
    /**
     * @brief Update last update time to current time
     */
    void UpdateLastUpdateTime();
    
    // =========================================================================
    // Virtual Interface
    // =========================================================================
    
    /**
     * @brief Update character logic
     * @param delta_time Time elapsed since last update (milliseconds)
     * @details Virtual method for derived class-specific updates
     */
    virtual void Update(UInt32 delta_time) = 0;
    
    /**
     * @brief Get character type identifier
     * @return Character type (for RTTI purposes)
     */
    virtual UInt32 GetCharacterType() const = 0;
    
    /**
     * @brief Check if this is a player character
     * @return true if player, false otherwise
     */
    virtual bool IsPlayer() const { return false; }
    
    /**
     * @brief Check if this is a monster
     * @return true if monster, false otherwise
     */
    virtual bool IsMonster() const { return false; }
    
    /**
     * @brief Check if this is an NPC
     * @return true if NPC, false otherwise
     */
    virtual bool IsNPC() const { return false; }

protected:
    // =========================================================================
    // Protected Methods
    // =========================================================================
    
    /**
     * @brief Initialize character data
     * @param create_data Creation parameters
     * @return true if initialization successful, false otherwise
     */
    bool InitializeCharacterData(const CharacterCreateData& create_data);
    
    /**
     * @brief Validate character data
     * @return true if data is valid, false otherwise
     */
    bool ValidateCharacterData() const;

private:
    // =========================================================================
    // Member Variables
    // =========================================================================
    
    // Core character data
    CharacterSerial character_serial_;          ///< Unique character identifier
    char name_[MAX_CHARACTER_NAME_LENGTH];     ///< Character name
    CharacterRace race_;                        ///< Character race
    CharacterClass class_type_;                 ///< Character class
    Level level_;                               ///< Character level
    bool is_initialized_;                       ///< Initialization status
    bool is_active_;                            ///< Active state
    
    // Position and location
    Position3D current_position_;               ///< Current 3D position
    MapIndex current_map_;                      ///< Current map index
    ZoneIndex current_zone_;                    ///< Current zone index
    
    // Timing
    GameTime creation_time_;                    ///< When character was created
    GameTime last_update_time_;                 ///< Last update time
    
    // Thread safety
    mutable std::mutex position_mutex_;         ///< Mutex for position updates
    mutable std::mutex data_mutex_;             ///< Mutex for general data access
    
    // Character type constants
    static constexpr UInt32 CHARACTER_TYPE_BASE = 0;
    static constexpr UInt32 CHARACTER_TYPE_PLAYER = 1;
    static constexpr UInt32 CHARACTER_TYPE_MONSTER = 2;
    static constexpr UInt32 CHARACTER_TYPE_NPC = 3;
};

// =============================================================================
// Utility Functions
// =============================================================================

/**
 * @brief Calculate 3D distance between two positions
 * @param pos1 First position
 * @param pos2 Second position
 * @return Distance between positions
 */
float CalculateDistance3D(const Position3D& pos1, const Position3D& pos2);

/**
 * @brief Calculate 2D distance between two positions (ignoring Z)
 * @param pos1 First position
 * @param pos2 Second position
 * @return 2D distance between positions
 */
float CalculateDistance2D(const Position3D& pos1, const Position3D& pos2);

/**
 * @brief Normalize a 3D vector
 * @param vector Vector to normalize (modified in place)
 * @return Length of original vector
 */
float NormalizeVector3D(Position3D& vector);

#endif // CHARACTER_H
