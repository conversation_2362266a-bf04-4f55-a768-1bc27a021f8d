/**
 * @file async_log_info.h
 * @brief Asynchronous logging system for RF Online Server
 * @details Refactored from CAsyncLogInfo - provides thread-safe logging capabilities
 * 
 * Original IDA Pro functions:
 * - CAsyncLogInfo::CAsyncLogInfo (0x1403BC9F0)
 * - CAsyncLogInfo::Init (0x1403BCB80)
 * - CAsyncLogInfo::GetCount (0x1403C16B0)
 * - CAsyncLogInfo::GetDirPath (0x1403C1630)
 * - CAsyncLogInfo::GetFileName (0x1403C16D0)
 * - CAsyncLogInfo::GetTypeName (0x1403C1650)
 * - CAsyncLogInfo::IncreaseCount (0x1403C16F0)
 * - CAsyncLogInfo::UpdateLogFileName (0x1403BD0F0)
 * 
 * <AUTHOR> Online Server Refactoring Project
 * @date 2025
 */

#ifndef ASYNC_LOG_INFO_H
#define ASYNC_LOG_INFO_H

#include "../../include/rf_types.h"
#include "../../include/rf_constants.h"
#include <mutex>
#include <memory>

// =============================================================================
// Forward Declarations
// =============================================================================

class LogFile;
class Timer;

// =============================================================================
// Enumerations
// =============================================================================

/**
 * @brief Types of asynchronous logging
 * @note Replaces ASYNC_LOG_TYPE enum from original code
 */
enum class AsyncLogType : UInt32 {
    NONE = 0,
    SYSTEM_ERROR = 1,
    GAME_EVENT = 2,
    PLAYER_ACTION = 3,
    COMBAT_LOG = 4,
    TRADE_LOG = 5,
    GUILD_LOG = 6,
    ADMIN_LOG = 7,
    SECURITY_LOG = 8,
    DATABASE_LOG = 9,
    NETWORK_LOG = 10,
    PERFORMANCE_LOG = 11,
    DEBUG_LOG = 12,
    BILLING_LOG = 13,
    AUTHENTICATION_LOG = 14,
    MAX_LOG_TYPES = 15
};

// =============================================================================
// AsyncLogInfo Class
// =============================================================================

/**
 * @brief Asynchronous logging information manager
 * @details Manages logging configuration and state for different log types
 * 
 * This class replaces the original CAsyncLogInfo and provides:
 * - Thread-safe logging operations
 * - Configurable log file management
 * - Automatic file rotation based on time
 * - Log count tracking
 * - Modern C++ memory management
 */
class AsyncLogInfo {
public:
    // =========================================================================
    // Constructor and Destructor
    // =========================================================================
    
    /**
     * @brief Default constructor
     * @details Initializes all members to default values
     * 
     * Replaces: CAsyncLogInfo::CAsyncLogInfo (0x1403BC9F0)
     */
    AsyncLogInfo();
    
    /**
     * @brief Destructor
     * @details Cleans up allocated resources
     */
    ~AsyncLogInfo();
    
    // Disable copy constructor and assignment operator
    AsyncLogInfo(const AsyncLogInfo&) = delete;
    AsyncLogInfo& operator=(const AsyncLogInfo&) = delete;
    
    // =========================================================================
    // Initialization and Configuration
    // =========================================================================
    
    /**
     * @brief Initialize the async log info
     * @param log_type Type of logging this instance handles
     * @param directory_path Directory where log files will be stored
     * @param type_name Human-readable name for this log type
     * @param add_date_to_filename Whether to include date in filename
     * @param update_filename_delay_ms Delay in milliseconds for filename updates (0 = no updates)
     * @param loading_log Optional log file for initialization messages
     * @return true if initialization successful, false otherwise
     * 
     * Replaces: CAsyncLogInfo::Init (0x1403BCB80)
     */
    bool Initialize(AsyncLogType log_type,
                   const char* directory_path,
                   const char* type_name,
                   bool add_date_to_filename = true,
                   UInt32 update_filename_delay_ms = 0,
                   LogFile* loading_log = nullptr);
    
    // =========================================================================
    // Accessors
    // =========================================================================
    
    /**
     * @brief Get the current log count
     * @return Number of log entries written
     * 
     * Replaces: CAsyncLogInfo::GetCount (0x1403C16B0)
     */
    UInt32 GetLogCount() const;
    
    /**
     * @brief Get the log directory path
     * @return Pointer to directory path string
     * 
     * Replaces: CAsyncLogInfo::GetDirPath (0x1403C1630)
     */
    const char* GetDirectoryPath() const;
    
    /**
     * @brief Get the current log filename
     * @return Pointer to filename string
     * 
     * Replaces: CAsyncLogInfo::GetFileName (0x1403C16D0)
     */
    const char* GetFileName() const;
    
    /**
     * @brief Get the type name
     * @return Pointer to type name string
     * 
     * Replaces: CAsyncLogInfo::GetTypeName (0x1403C1650)
     */
    const char* GetTypeName() const;
    
    /**
     * @brief Get the log type
     * @return The AsyncLogType for this instance
     */
    AsyncLogType GetLogType() const { return log_type_; }
    
    /**
     * @brief Check if this log info is initialized
     * @return true if initialized, false otherwise
     */
    bool IsInitialized() const { return is_initialized_; }
    
    // =========================================================================
    // Operations
    // =========================================================================
    
    /**
     * @brief Increment the log count
     * @details Thread-safe increment of the log counter
     * 
     * Replaces: CAsyncLogInfo::IncreaseCount (0x1403C16F0)
     */
    void IncrementLogCount();
    
    /**
     * @brief Update the log filename
     * @details Updates filename with current timestamp if configured
     * 
     * Replaces: CAsyncLogInfo::UpdateLogFileName (0x1403BD0F0)
     */
    void UpdateLogFileName();
    
    /**
     * @brief Reset the log count
     * @details Sets log count back to zero
     */
    void ResetLogCount();

private:
    // =========================================================================
    // Private Methods
    // =========================================================================
    
    /**
     * @brief Generate filename with current date/time
     * @param base_name Base name for the file
     * @param include_date Whether to include date in filename
     * @return Generated filename
     */
    std::string GenerateFileName(const std::string& base_name, bool include_date) const;
    
    /**
     * @brief Get current date/time string
     * @return Formatted date/time string
     */
    std::string GetCurrentDateTimeString() const;
    
    // =========================================================================
    // Member Variables
    // =========================================================================
    
    AsyncLogType log_type_;                     ///< Type of logging
    std::atomic<UInt32> log_count_;            ///< Number of log entries (thread-safe)
    std::string directory_path_;                ///< Directory for log files
    std::string filename_;                      ///< Current log filename
    std::string type_name_;                     ///< Human-readable type name
    bool add_date_to_filename_;                ///< Whether to include date in filename
    bool is_initialized_;                       ///< Initialization status
    
    std::unique_ptr<Timer> update_timer_;       ///< Timer for filename updates
    mutable std::mutex mutex_;                  ///< Mutex for thread safety
    
    // Constants
    static constexpr UInt32 MIN_UPDATE_DELAY_MS = 10000;  ///< Minimum update delay (10 seconds)
    static constexpr size_t MAX_PATH_LENGTH = 512;        ///< Maximum path length
};

#endif // ASYNC_LOG_INFO_H
