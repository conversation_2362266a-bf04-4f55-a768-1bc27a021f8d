/*
 * Function: ?Validate@?$DL_PrivateKeyImpl@VDL_GroupParameters_GFP_DefaultSafePrime@CryptoPP@@@CryptoPP@@UEBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x140637C30
 */

char __fastcall CryptoPP::DL_PrivateKeyImpl<CryptoPP::DL_GroupParameters_GFP_DefaultSafePrime>::Validate(__int64 a1, __int64 a2, unsigned int a3)
{
  __int64 v3; // rax@1
  char v4; // ST30_1@1
  __int64 v5; // rax@1
  CryptoPP::Integer *v6; // rax@1
  CryptoPP::Integer *v7; // rax@1
  CryptoPP::Integer *v8; // rax@8
  CryptoPP::Integer *a; // [sp+20h] [bp-A8h]@1
  CryptoPP::Integer *b; // [sp+28h] [bp-A0h]@1
  char v12; // [sp+30h] [bp-98h]@6
  CryptoPP::Integer v13; // [sp+40h] [bp-88h]@8
  int v14; // [sp+68h] [bp-60h]@1
  __int64 v15; // [sp+70h] [bp-58h]@1
  int (__fastcall **v16)(_QWORD); // [sp+78h] [bp-50h]@1
  int (__fastcall **v17)(_QWORD); // [sp+80h] [bp-48h]@1
  __int64 v18; // [sp+88h] [bp-40h]@1
  __int64 v19; // [sp+90h] [bp-38h]@1
  int v20; // [sp+98h] [bp-30h]@4
  CryptoPP::Integer *v21; // [sp+A0h] [bp-28h]@8
  CryptoPP::Integer *v22; // [sp+A8h] [bp-20h]@8
  CryptoPP::Integer *v23; // [sp+B0h] [bp-18h]@8
  int v24; // [sp+B8h] [bp-10h]@9
  __int64 v25; // [sp+D0h] [bp+8h]@1
  __int64 v26; // [sp+D8h] [bp+10h]@1
  unsigned int v27; // [sp+E0h] [bp+18h]@1

  v27 = a3;
  v26 = a2;
  v25 = a1;
  v15 = -2i64;
  v14 = 0;
  v16 = *(int (__fastcall ***)(_QWORD))(a1 - 392);
  LODWORD(v3) = (*v16)(a1 - 392);
  v4 = (*(int (__fastcall **)(signed __int64, __int64, _QWORD))(*(_QWORD *)(v3
                                                                          + *(_DWORD *)(*(_QWORD *)(v3 + 8) + 4i64)
                                                                          + 8)
                                                              + 24i64))(
         v3 + *(_DWORD *)(*(_QWORD *)(v3 + 8) + 4i64) + 8,
         v26,
         v27);
  v17 = *(int (__fastcall ***)(_QWORD))(v25 - 392);
  LODWORD(v5) = (*v17)(v25 - 392);
  v18 = v5;
  LODWORD(v6) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v5 + 64i64))(v5);
  b = v6;
  v19 = *(_QWORD *)(v25 - 392);
  LODWORD(v7) = (*(int (__fastcall **)(signed __int64))(v19 + 16))(v25 - 392);
  a = v7;
  v20 = v4 && CryptoPP::Integer::IsPositive(v7) && CryptoPP::operator<(a, b);
  v12 = v20;
  if ( v27 >= 1 )
  {
    v24 = (_BYTE)v20
       && (LODWORD(v8) = CryptoPP::Integer::One(),
           v21 = v8,
           v22 = CryptoPP::Integer::Gcd(&v13, a, b),
           v23 = v22,
           v14 |= 1u,
           CryptoPP::operator==(v22, v21));
    v12 = v24;
    if ( v14 & 1 )
    {
      v14 &= 0xFFFFFFFE;
      CryptoPP::Integer::~Integer(&v13);
    }
  }
  return v12;
}
