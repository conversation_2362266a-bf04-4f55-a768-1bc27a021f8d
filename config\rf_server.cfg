# RF Online Server Configuration File
# This file contains configuration settings for the refactored RF Online Server
# Lines starting with # are comments and are ignored

[Server]
# Server name displayed to clients
ServerName=RF Online Server - Refactored Edition

# Server version
Version=*******

# Maximum number of concurrent players
MaxPlayers=10000

# Server tick rate (updates per second)
TickRate=20

# Enable debug mode (more verbose logging)
DebugMode=true

[Network]
# Server listening port
Port=27015

# Maximum packet size in bytes
MaxPacketSize=8192

# Network timeout in milliseconds
TimeoutMs=30000

# Heartbeat interval in milliseconds
HeartbeatIntervalMs=10000

[Database]
# Database connection string
ConnectionString=Server=localhost;Database=RFOnline;Trusted_Connection=true;

# Database timeout in seconds
TimeoutSeconds=30

# Maximum number of concurrent database connections
MaxConnections=10

# Enable database connection pooling
UseConnectionPooling=true

[Logging]
# Log directory path
LogDirectory=logs

# Minimum log level (Trace, Debug, Info, Warning, Error, Critical)
MinLogLevel=Info

# Enable console output
EnableConsoleOutput=true

# Enable file output
EnableFileOutput=true

# Enable timestamps in log messages
EnableTimestamps=true

# Log file rotation interval in milliseconds (0 = no rotation)
RotationIntervalMs=3600000

[World]
# World update interval in milliseconds
UpdateIntervalMs=50

# Maximum monsters per map
MaxMonstersPerMap=1000

# Monster respawn check interval in milliseconds
RespawnCheckIntervalMs=10000

# Enable monster AI
EnableMonsterAI=true

[Combat]
# Maximum aggro entries per monster
MaxAggroEntries=10

# Aggro decay rate per second
AggroDecayRate=1

# Combat timeout in milliseconds
CombatTimeoutMs=30000

[Loot]
# Loot rights duration in milliseconds
LootRightsDurationMs=60000

# Loot expire time in milliseconds
LootExpireTimeMs=300000

# Maximum loot items per monster
MaxLootItems=10

[Performance]
# Number of worker threads
WorkerThreads=4

# Enable multi-threading
EnableMultiThreading=true

# Memory pool size for objects
ObjectPoolSize=10000

# Garbage collection interval in milliseconds
GCIntervalMs=60000

[Security]
# Maximum login attempts before lockout
MaxLoginAttempts=3

# Login lockout duration in milliseconds
LoginLockoutMs=300000

# Maximum packet rate per second per client
MaxPacketRate=100

# Enable anti-speed hack detection
EnableSpeedHackDetection=true

# Speed hack threshold (multiplier of normal speed)
SpeedHackThreshold=1.5

[Features]
# Enable specific features (true/false)
EnableLogging=true
EnableMonsterAI=true
EnablePlayerSystem=true
EnableDatabase=true
EnableNetworking=true
EnableLuaScripting=false
EnablePhysics=false
EnableAudio=false

[Paths]
# Data directory path
DataDirectory=data

# Script directory path
ScriptDirectory=scripts

# Configuration directory path
ConfigDirectory=config

# Backup directory path
BackupDirectory=backup

[Maintenance]
# Auto-save interval in milliseconds
AutoSaveIntervalMs=300000

# Cleanup interval in milliseconds
CleanupIntervalMs=600000

# Enable automatic backups
EnableAutoBackup=true

# Backup interval in milliseconds
BackupIntervalMs=3600000

# Maximum number of backup files to keep
MaxBackupFiles=24
